package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.banks.cybk.enums.CYBKReccFileType;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.CYBKReccRepay;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class CYBKReccRepayHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKReccAbstractHandler.class);

    @Override
    public void process(LocalDate reccDay) {
        //对资正常还款，非代偿
        List<BankRepayRecord> repayRecords = findSuccessBankRepayRecordNormal(reccDay, false);
        CYBKReconcileFile reconcileFile = findReconcileFile(reccDay, CYBKReccFileType.REPAYMENT_FILE);
        reconcileFile.setReccDate(LocalDate.now());

        String reccId = reconcileFile.getId();
        List<CYBKReccRepay> records = findReccRepayFileRecords(reccId);
        List<CYBKReccRepay> reccRepayRecords = records.stream().filter(lf -> "N".equals(lf.getIsClaim())).toList();

        if (repayRecords.size() == 0 && reccRepayRecords.size() == 0) {
            reconcileFile.setReccState(ReccStateEnum.S.name());
            updateCYBKReconcileFile(reconcileFile);
            return;
        }
        if (repayRecords.size() != reccRepayRecords.size()) {
            logger.warn("长银直连对账成功条数不一致 reccType：{} reccDay：{} 业务方条数：{} 资方条数：{}", CYBKReccFileType.REPAYMENT_FILE, reccDay, repayRecords.size(),
                reccRepayRecords.size());
            getWarningService().warn("\n长银直连对账:" + CYBKReccFileType.REPAYMENT_FILE + "\n对账日:" + reccDay + "\n成功条数不一致 ");
        }
        List<CYBKReccRepay> successList = new ArrayList<>();
        reccRepayRecords.forEach(lf -> {
            var loanId = lf.getSysId();
            var existRecord = filterRepayInter(repayRecords, loanId, lf.getBankRepayNo());
            boolean match = match(lf, existRecord);
            lf.setReccStatus(match ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
            if (match) {
                successList.add(lf);
            } else {
                //对账失败打印日志
                warningLog(lf, existRecord, loanId);
            }
            updateReccRepay(lf);
        });
        boolean allMatch = successList.size() == reccRepayRecords.size() && successList.size() == repayRecords.size();
        reconcileFile.setReccState(allMatch ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
        updateCYBKReconcileFile(reconcileFile);
        //对账失败，企业微信告警
        if (ReccStateEnum.F.name().equals(reconcileFile.getReccState())) {
            getWarningService().warn("\n长银直连对账失败:" + CYBKReccFileType.REPAYMENT_FILE + "\n对账日:" + reccDay + "\n，对账成功总数与系统还款总数不一致 ");
        }
    }


    private BankRepayRecord filterRepayInter(List<BankRepayRecord> recordList, String loanId, String bankRepayNo) {
        return recordList.stream().filter(record -> record.getLoanId().equals(loanId) && bankRepayNo.equals(record.getBankSerial())).findAny().orElse(null);
    }

    private boolean match(CYBKReccRepay reccRepay, BankRepayRecord repayRecord) {
        if (repayRecord == null) {
            return false;
        }
        if (ProcessStatus.SUCCESS != repayRecord.getRepayStatus()) {
            return false;
        }

        // 系统 本金+利息+罚息+融担费
        BigDecimal sysReduceAmt = repayRecord.getPrincipalAmt().add(repayRecord.getInterestAmt()).add(repayRecord.getPenaltyAmt())
            .add(repayRecord.getGuaranteeAmt());
        // 资方 本金+利息+罚息+融担费
        BigDecimal bankReduceAmt = reccRepay.getActPrincipalAmt().add(reccRepay.getActInterestAmt().
            add(reccRepay.getActPenaltyAmt()).add(reccRepay.getActGuaranteeFeeAmt()));

        //资方的总金额里不包含融担费
        BigDecimal sysTotalAmount = repayRecord.getTotalAmt().subtract(repayRecord.getGuaranteeAmt());
        return sysTotalAmount.compareTo(reccRepay.getActRepayAmount()) == 0
            && sysReduceAmt.compareTo(bankReduceAmt) == 0;
    }


    private void warningLog(CYBKReccRepay lf, BankRepayRecord existRecord, String loanId) {
        BigDecimal sysAmount = null;
        Integer sysPeriod = null;
        if (Objects.nonNull(existRecord)) {
            sysAmount = existRecord.getTotalAmt();
            sysPeriod = existRecord.getPeriod();
        }
        logger.warn("长银直连对账失败，reccType：{} 资方loanId：{}，还款总金额:{}; 业务方总金额:{},期数:{}",
            CYBKReccFileType.REPAYMENT_FILE, loanId, lf.getActRepayAmount(), sysAmount, sysPeriod);
    }

    @Override
    public ReccType getReccType() {
        return ReccType.REPAY;
    }
}
