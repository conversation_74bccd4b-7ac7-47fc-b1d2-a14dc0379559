package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.banks.cybk.config.CYBKSftpConfig;
import com.jinghang.capital.core.banks.cybk.convert.CYBKReccConvert;
import com.jinghang.capital.core.banks.cybk.enums.CYBKReccFileType;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKCustDailyLoan;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKCustDailyLoanPlan;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKLoanReplanDTO;
import com.jinghang.capital.core.banks.cybk.remote.CYBKSftpService;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.LoanStatus;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.*;
import com.jinghang.capital.core.service.WarningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.springframework.beans.BeanUtils.getPropertyDescriptors;

/**
 * <AUTHOR>
 * @date 2023/8/28 15:36
 */
public abstract class CYBKReccAbstractHandler implements CYBKReccHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKReccAbstractHandler.class);
    private static final int TWO = 2;
    private static final int SIX = 6;
    private static final int THIRTY_ONE = 31;

    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private CreditRepository creditRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private LoanReplanRepository loanReplanRepository;

    @Autowired
    private CustomerRepayRecordRepository customerRepayRecordRepository;

    @Autowired
    private CYBKReconcileFileRepository cybkReconcileFileRepository;

    @Autowired
    private CYBKReccLoanRepository cybkReccLoanRepository;

    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    @Autowired
    private BankLoanReplanRepository bankLoanReplanRepository;

    @Autowired
    private CYBKReccRepayRepository cybkReccRepayRepository;

    @Autowired
    private CYBKReccPlanRepository cybkReccPlanRepository;

    @Autowired
    private CYBKReccRepayDetailRepository cybkReccRepayDetailRepository;

    @Autowired
    private CYBKReccClaimRepository cybkReccClaimRepository;

    @Autowired
    private CYBKCreditFlowRepository cybKCreditFlowRepository;

    @Autowired
    private CustomerLoanReplanRepository customerLoanReplanRepository;

    @Autowired
    private CYBKConfig cybkConfig;

    @Autowired
    private CYBKSftpConfig sftpConfig;

    @Autowired
    private CYBKSftpService cybkSftpService;

    @Autowired
    private WarningService warningService;

    public WarningService getWarningService() {
        return warningService;
    }

    public CYBKReconcileFile findReconcileFile(LocalDate reccDay, CYBKReccFileType reccFileType) {
        List<CYBKReconcileFile> reconFiles = cybkReconcileFileRepository.findByChannelAndFileDateAndReccType(BankChannel.CYBK.name(),
            reccDay, reccFileType.name());
        long hasSuccessCount = reconFiles.stream().filter(rf -> ReccStateEnum.S.name().equals(rf.getReccState())).count();
        if (hasSuccessCount > 0) {
            logger.error("CYBK recc process reccDay: {}, type: {} has success processed.", reccDay, reccFileType);
            warningService.warn("\n长银直连对账:" + reccFileType.name() + "\n对账日:" + reccDay + "\n已经对账成功");
            throw new BizException(BizErrorCode.REC_HAS_SUCCESS);
        }

        List<CYBKReconcileFile> list = reconFiles.stream().filter(rf -> ReccStateEnum.P.name().equals(rf.getReccState())).toList();

        if (list.size() == 0) {
            logger.error("CYBK recc process reccDay: {}, type: {} has no process file.", reccDay, reccFileType);
            warningService.warn("\n长银直连对账:" + reccFileType.name() + "\n对账日:" + reccDay + "\n没有待处理记录");
            throw new BizException(BizErrorCode.REC_NOT_FOUND);
        }

        if (list.size() > 1) {
            logger.error("CYBK recc process reccDay: {}, type: {} has multi process file.", reccDay, reccFileType);
            warningService.warn("\n长银直连对账:" + reccFileType.name() + "\n对账日:" + reccDay + "\n存在多条待处理记录");
            throw new BizException(BizErrorCode.REC_MULTI_PROCESSING_FILE);
        }
        return list.get(0);

    }

    protected CYBKCustDailyLoanPlan getCsvData(LocalDate reccDay, CYBKLoanReplanDTO dto, SimpleDateFormat sdf) {
        CYBKCustDailyLoanPlan plan = new CYBKCustDailyLoanPlan();
        LocalDate repayDate = new Date(dto.getRepayDate().getTime()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Loan loan = getLoanRepository().findById(dto.getLoanId()).orElseThrow();
        Credit credit = getCreditRepository().findById(dto.getCreditId()).orElseThrow();

        Date startDate;
        String status = getStatus(dto);
        String clearDate = "";
        String principalDate = "";
        String interestDate = "";
        String principalOverdueDays = "0";
        BigDecimal repayPenalty = BigDecimal.ZERO;
        int overdueDays = (int) Math.max(dto.getOverdueDays(), 0);


        // 本期次起始日
        if (1 == dto.getPeriod()) {
            startDate = dto.getLoanTime();
        } else {
            LoanReplan lastPlan = getLoanReplanRepository().findByLoanIdAndPeriod(dto.getLoanId(), dto.getPeriod() - 1).orElseThrow();
            startDate = Date.from(lastPlan.getRepayDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        }

        //对客已还,取还款时间
        if (RepayStatus.REPAID.name().equals(dto.getCustRepayStatus())) {
            //已还后,逾期天数置为0
            overdueDays = 0;
            dto.setPrincipalAmt(BigDecimal.ZERO);
            dto.setInterestAmt(BigDecimal.ZERO);
            List<CustomerRepayRecord> custRepayList = getCustomerRepayRecordRepository().findByLoanIdAndRepayStatus(dto.getLoanId(), ProcessStatus.SUCCESS);
            //当期对客还款记录
            CustomerRepayRecord currentPeriod = custRepayList.stream().filter(item -> item.getPeriod().equals(dto.getPeriod())).findFirst().orElse(null);
            if (currentPeriod != null) {
                clearDate = currentPeriod.getRepayTime().format(DateTimeFormatter.ISO_LOCAL_DATE);
            } else {
                //取最后一期还款记录的时间
                CustomerRepayRecord customerRepayRecord = custRepayList.stream().max(Comparator.comparing(CustomerRepayRecord::getPeriod)).orElseThrow();
                clearDate = customerRepayRecord.getRepayTime().format(DateTimeFormatter.ISO_LOCAL_DATE);
            }
        }

        //对资已还
        if (RepayStatus.REPAID.name().equals(dto.getBankRepayStatus())) {
            repayPenalty = dto.getPenaltyAmt();
        }

        //逾期
        if (overdueDays > 0) {
            principalDate = repayDate.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
            interestDate = repayDate.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
            principalOverdueDays = String.valueOf(overdueDays);

            //剩余未还本金
            BigDecimal unpaidPrincipal = getLoanReplanRepository().findByLoanIdAndCustRepayStatusOrderByPeriod(dto.getLoanId(), RepayStatus.NORMAL)
                .stream().map(LoanReplan::getPrincipalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

            // 罚息日利率
            BigDecimal penaltyRateDay = loan.getBankRate().divide(new BigDecimal("360"), SIX, RoundingMode.HALF_UP);
            // 对资罚息：资方罚息 = 逾期金额(当期本金) * 罚息日利率(0.15/360) * 逾期天数
            repayPenalty = unpaidPrincipal.multiply(penaltyRateDay).multiply(new BigDecimal(overdueDays))
                .setScale(TWO, RoundingMode.HALF_UP);
        }

        plan.setLoanSeq(dto.getLoanId());
        plan.setApplSeq(credit.getCreditNo());
        plan.setOutApplSeq(credit.getId()); //授信申请流水号
        plan.setSettleDate(reccDay.format(DateTimeFormatter.ISO_LOCAL_DATE));
        plan.setPeriod(dto.getPeriod());
        plan.setPeriods(dto.getPeriods());
        plan.setLoanTime(dto.getLoanTime());
        plan.setGuaranteeContractNo(dto.getGuaranteeContractNo());
        plan.setStartDate(sdf.format(startDate));
        plan.setEndDate(sdf.format(dto.getRepayDate()));
        plan.setStatus(status);
        plan.setClearDate(status);
        plan.setClearDate(clearDate);
        plan.setPrincipalDate(principalDate);
        plan.setInterestDate(interestDate);
        plan.setOverdueDays(principalOverdueDays);
        plan.setPrincipal(dto.getPrincipalAmt());
        plan.setInterest(dto.getInterestAmt());
        plan.setPenalty(repayPenalty);
        plan.setGuarantee(dto.getGuaranteeAmt());
        plan.setAccrued("0");
        plan.setWriteOff("N");


        return plan;
    }


    protected Map<String, CYBKCustDailyLoan> getDailyLoanMap(List<CYBKLoanReplanDTO> allList, LocalDate reccDay) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        //按loanId、period升序
        allList = allList.stream().sorted(Comparator.comparing(CYBKLoanReplanDTO::getLoanId).thenComparing(CYBKLoanReplanDTO::getPeriod)).toList();

        //转换成资方文件对象(还款计划维度)
        List<CYBKCustDailyLoanPlan> planList = allList.stream()
            .map(dto -> getCsvData(reccDay, dto, dateFormat))
            .collect(Collectors.toList());

        // 按loanId分组
        Map<String, List<CYBKCustDailyLoanPlan>> planMap = groupByLoanId(planList);

        // 按loanId汇总成借据维度
        return summaryToDailyLoan(reccDay, planMap);
    }

    private Map<String, List<CYBKCustDailyLoanPlan>> groupByLoanId(List<CYBKCustDailyLoanPlan> planList) {
        //按LoanId分组
        Map<String, List<CYBKCustDailyLoanPlan>> planMap = new HashMap<>();
        for (CYBKCustDailyLoanPlan plan : planList) {
            List<CYBKCustDailyLoanPlan> list = planMap.get(plan.getOutApplSeq());
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(plan);
            planMap.put(plan.getLoanSeq(), list);
        }
        return planMap;
    }

    private Map<String, CYBKCustDailyLoan> summaryToDailyLoan(LocalDate reccDay, Map<String, List<CYBKCustDailyLoanPlan>> planMap) {
        Map<String, CYBKCustDailyLoan> loanMap = new HashMap<>();
        for (String loanId : planMap.keySet()) {
            List<CYBKCustDailyLoanPlan> list = planMap.get(loanId);

            int overdueDays = 0;
            int overduePeriods;
            int unpaidPeriods;
            //下一还款日期,如果有逾期,则为次日
            String nextRepayDate = "";


            //按期次号排序
            list = list.stream().sorted(Comparator.comparing(CYBKCustDailyLoanPlan::getPeriod)).toList();
            //逾期计划数
            overduePeriods = getLoanReplanRepository().countByLoanIdAndCustRepayStatusAndRepayDateIsBefore(loanId, RepayStatus.NORMAL, reccDay);
            //未还期数
            unpaidPeriods = getLoanReplanRepository().countByLoanIdAndCustRepayStatus(loanId, RepayStatus.NORMAL);
            //所有未还期数
            List<LoanReplan> unpaidPlans = getLoanReplanRepository().findByLoanIdAndCustRepayStatusOrderByPeriod(loanId, RepayStatus.NORMAL);
            //最小未还计划
            LoanReplan minUnpaid = unpaidPlans.stream().min(Comparator.comparing(LoanReplan::getPeriod)).orElse(null);
            if (minUnpaid != null) {
                nextRepayDate = minUnpaid.getRepayDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
            }
            //首次逾期的计划
            LoanReplan firstOverdue = unpaidPlans.stream().filter(item -> RepayStatus.NORMAL == item.getCustRepayStatus()
                    && reccDay.isAfter(item.getRepayDate()))
                .findFirst().orElse(null);
            //有逾期
            if (firstOverdue != null) {
                //取最大逾期天数
                long overduesDay = ChronoUnit.DAYS.between(firstOverdue.getRepayDate(), reccDay);
                if (overduesDay > overdueDays) {
                    overdueDays = (int) overduesDay;
                }
                nextRepayDate = reccDay.plusDays(1L).format(DateTimeFormatter.ISO_LOCAL_DATE);
            }

            CYBKCustDailyLoan dailyLoan = loanMap.get(loanId);
            if (dailyLoan == null) {
                dailyLoan = CYBKReccConvert.INSTANCE.toDailyLoan(list.get(0));
            }

            overdueDays = Math.max(overdueDays, 0);
            dailyLoan.setOverdueDays(String.valueOf(overdueDays));
            dailyLoan.setOverduePeriods(overduePeriods);
            dailyLoan.setUnpaidPeriods(unpaidPeriods);
            //状态
            String status = getDailyLoanStatus(unpaidPeriods, overdueDays);
            dailyLoan.setStatus(status);
            //五级分类
            String leverClass = getLeverClass(overdueDays);
            dailyLoan.setAssetClass(leverClass);
            //已结清
            if (unpaidPeriods == 0) {
                //取最后一期的还款时间
                List<CustomerRepayRecord> custRepayList = getCustomerRepayRecordRepository().findByLoanIdAndRepayStatus(loanId, ProcessStatus.SUCCESS);
                CustomerRepayRecord customerRepayRecord = custRepayList.stream().max(Comparator.comparing(CustomerRepayRecord::getPeriod)).orElseThrow();
                //结清日期
                dailyLoan.setClearDate(customerRepayRecord.getRepayTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
                nextRepayDate = customerRepayRecord.getRepayTime().format(DateTimeFormatter.ISO_LOCAL_DATE);
            }
            dailyLoan.setNextRepayDate(nextRepayDate);

            for (int i = 1; i < list.size(); i++) {
                CYBKCustDailyLoanPlan plan = list.get(i);
                dailyLoan.setPrincipal(dailyLoan.getPrincipal().add(plan.getPrincipal()));
                dailyLoan.setInterest(dailyLoan.getInterest().add(plan.getInterest()));
                dailyLoan.setPenalty(dailyLoan.getPenalty().add(plan.getPenalty()));
                dailyLoan.setGuarantee(dailyLoan.getPenalty().add(plan.getPenalty()));
            }
            loanMap.put(loanId, dailyLoan);
        }
        return loanMap;
    }

    private String getDailyLoanStatus(Integer unpaidPeriods, int overdueDays) {
        if (unpaidPeriods == 0) {
            return "CLEAR";
        } else if (overdueDays > 0) {
            return "OVD";
        } else {
            return "NORMAL";
        }
    }

    private String getLeverClass(int overdueDays) {
        if (overdueDays == 0) {
            return "1";
        } else if (overdueDays > 1 && overdueDays < THIRTY_ONE) {
            return "2";
        } else {
            return "5";
        }
    }

    private String getStatus(CYBKLoanReplanDTO dto) {
        String status;
        if (RepayStatus.REPAID.name().equals(dto.getCustRepayStatus())) {
            status = "CLEAR";
        } else {
            if (dto.getOverdueDays() > 0) {
                status = "OVD";
            } else {
                status = "NORMAL";
            }
        }
        return status;
    }


    public List<CustomerRepayRecord> findCustReccRepay(LocalDate reccDay) {
        LocalDateTime dayStart = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return customerRepayRecordRepository.findCustReccRepay(BankChannel.CYBK, ProcessStatus.SUCCESS, dayStart, nextDayStart);
    }

    public List<CYBKLoanReplanDTO> findCustReccLoanReplans(LocalDate reccDay) {
        LocalDateTime dayStart = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return loanReplanRepository.findCYBKLoanReplanByChannelAndLoanStatusAndLoanTime(
            BankChannel.CYBK.getCode(), LoanStatus.SUCCESS.name(), dayStart, nextDayStart);
    }

    public List<CYBKLoanReplanDTO> findCYBKLoanPlanDailyHasChanged(LocalDate reccDay) {
        LocalDateTime dayStart = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return loanReplanRepository.findCYBKLoanPlanDailyHasChanged(BankChannel.CYBK.getCode(), reccDay, dayStart, nextDayStart);
    }

    public List<String> findCustReccLoans(LocalDate reccDay) {
        LocalDateTime dayStart = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return findLoans(dayStart, nextDayStart);
    }

    public List<Loan> findReccLoans(LocalDate reccDay) {
        LocalDateTime dayStart = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return findLoans0(dayStart, nextDayStart);
    }

    /**
     * 查询指定时间段内成功的借据
     */
    public List<Loan> findLoans0(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        return loanRepository.findByChannelAndLoanStatusAndLoanTime(
            BankChannel.CYBK, LoanStatus.SUCCESS, dayStart, nextDayStart);
    }

    public List<String> findLoans(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        return loanRepository.findCYBKLoanByChannelAndLoanStatusAndLoanTime(
            BankChannel.CYBK.getCode(), LoanStatus.SUCCESS.name(), dayStart, nextDayStart);
    }

    public List<CYBKReccLoan> findReccLoanFileRecords(String reccId) {
        return cybkReccLoanRepository.findByReccId(reccId);
    }

    public List<BankRepayRecord> findSuccessBankRepayRecordNormal(LocalDate reccDay, boolean isClaim) {
        LocalDateTime dayStart = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return isClaim ? findSuccessBankRepayRecordClaim(dayStart, nextDayStart) : findSuccessBankRepayRecordNotClaim(dayStart, nextDayStart);
    }

    /**
     * 对资正常还款
     */
    public List<BankRepayRecord> findSuccessBankRepayRecordNotClaim(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        List<BankRepayRecord> records = findSuccessBankRepayRecord(dayStart, nextDayStart);
        return records.stream().filter(r -> r.getRepayType() != RepayType.CLAIM).toList();
    }

    /**
     * 对资代偿还款
     */
    public List<BankRepayRecord> findSuccessBankRepayRecordClaim(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        List<BankRepayRecord> records = findSuccessBankRepayRecord(dayStart, nextDayStart);
        return records.stream().filter(r -> r.getRepayType() == RepayType.CLAIM).toList();
    }

    public List<BankRepayRecord> findSuccessBankRepayRecord(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        return bankRepayRecordRepository.findByRepayStatusAndChannelAndRepayTimeIsGreaterThanEqualAndRepayTimeIsLessThan(
            ProcessStatus.SUCCESS, BankChannel.CYBK, dayStart, nextDayStart);
    }


    public List<CYBKReccRepay> findReccRepayFileRecords(String reccId) {
        return cybkReccRepayRepository.findByReccId(reccId);
    }

    public List<CYBKReccRepayDetail> findReccRepayDetailFileRecords(String reccId) {
        return cybkReccRepayDetailRepository.findByReccId(reccId);
    }

    public List<CYBKReccPlan> findReccPlanFileRecords(String reccId) {
        return cybkReccPlanRepository.findByReccId(reccId);
    }

    public CustomerRepayRecordRepository getCustomerRepayRecordRepository() {
        return customerRepayRecordRepository;
    }

    public List<CYBKReccClaim> findReccClaimFileRecords(String reccId) {
        return cybkReccClaimRepository.findByReccId(reccId);
    }

    public BankRepayRecord updateBankRepayRecord(BankRepayRecord record) {
        return bankRepayRecordRepository.save(record);
    }

    public BankLoanReplan updateBankPlan(BankLoanReplan plan) {
        return bankLoanReplanRepository.save(plan);
    }

    public BankLoanReplan findBankPlan(String loanId, Integer period) {
        return bankLoanReplanRepository.findByLoanIdAndPeriod(loanId, period).orElse(null);
    }

    public void updateReccClaimStatus(CYBKReccClaim reccClaim) {
        cybkReccClaimRepository.updateReccStatus(reccClaim.getReccStatus(), reccClaim.getId());
    }

    public CYBKReconcileFile updateCYBKReconcileFile(CYBKReconcileFile reconcileFile) {
        return cybkReconcileFileRepository.save(reconcileFile);
    }

    public CYBKReccLoan updateReccLoan(CYBKReccLoan reccLoan) {
        return cybkReccLoanRepository.save(reccLoan);
    }

    public CYBKReccRepay updateReccRepay(CYBKReccRepay reccLoan) {
        return cybkReccRepayRepository.save(reccLoan);
    }

    public CYBKReccPlan updateReccReplan(CYBKReccPlan reccPlan) {
        return cybkReccPlanRepository.save(reccPlan);
    }
    public CYBKReccRepayDetail updateReccRepayDetail(CYBKReccRepayDetail reccDetail) {
        return cybkReccRepayDetailRepository.save(reccDetail);
    }

    public CYBKReccClaim updateReccClaim(CYBKReccClaim reccClaim) {
        return cybkReccClaimRepository.save(reccClaim);
    }

    public CYBKConfig getCybkConfig() {
        return cybkConfig;
    }

    public CYBKSftpConfig getSftpConfig() {
        return sftpConfig;
    }

    public CYBKCreditFlowRepository getCybKCreditFlowRepository() {
        return cybKCreditFlowRepository;
    }

    public CreditRepository getCreditRepository() {
        return creditRepository;
    }

    public LoanRepository getLoanRepository() {
        return loanRepository;
    }

    public AccountRepository getAccountRepository() {
        return accountRepository;
    }

    public LoanReplanRepository getLoanReplanRepository() {
        return loanReplanRepository;
    }

    public CustomerLoanReplanRepository getCustomerLoanReplanRepository() {
        return customerLoanReplanRepository;
    }

    public CYBKSftpService getCybkSftpService() {
        return cybkSftpService;
    }

    public BankRepayRecordRepository getBankRepayRecordRepository() {
        return bankRepayRecordRepository;
    }

    public static <T> List<T> toList(List<Map<String, Object>> mapList, Class<T> clazz) throws IllegalAccessException, InstantiationException {
        if (mapList == null || clazz == null) {
            return null;
        }
        List<T> list = new ArrayList<>(mapList.size());
        for (Map<String, Object> map : mapList) {
            T t = clazz.newInstance();
            copyProperties(map, t);
            list.add(t);
        }
        return list;
    }

    public static void copyProperties(Map<String, Object> map, Object target) {
        if (map == null || target == null || map.isEmpty()) {
            return;
        }
        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);
        for (PropertyDescriptor targetPd : targetPds) {
            if (targetPd.getWriteMethod() == null) {
                continue;
            }
            try {
                String key = targetPd.getName();
                Object value = map.get(key);
                // 这里判断以下value是否为空
                setValue(target, targetPd, value);
            } catch (Exception ex) {
                //throw new FatalBeanException("Could not copy properties from source to target", ex);
            }
        }
    }

    private static void setValue(Object target, PropertyDescriptor targetPd, Object value) throws IllegalAccessException, InvocationTargetException {
        // 这里判断以下value是否为空
        if (value != null) {
            Method writeMethod = targetPd.getWriteMethod();
            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                writeMethod.setAccessible(true);
            }
            writeMethod.invoke(target, value);
        }
    }

    protected String getCustReccFilePath(LocalDate reccDay) {
        String dateStr = reccDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // / download/cyxf/{产品编码}/out/files/{YYYYMMDD}/
        return sftpConfig.getCybkSftpUploadDir() + cybkConfig.getLoanType() + "/out/files/" + dateStr + "/";
    }
}
