package com.jinghang.capital.core.banks.cybk.enums;

/**
 * 交易码
 */
public enum CYBKTradeCode {
    /**
     * 银行卡签约申请
     */
    BIND_CARD_APPLY("/protocol/sync/v2/", "扣款协议号同步"),
    /**
     * 银行卡变更
     */
    BANK_CARD_CHANGE("/changeAccount/apply/v2/", "银行卡变更"),
    /**
     * 授信申请
     */
    CREDIT_APPLY("/limit/credit/apply/", "授信申请"),
    /**
     * 授信申请进度查询
     */
    CREDIT_QUERY("/limit/credit/query/", "授信申请进度查询"),
    /**
     * 放款申请
     */
    LOAN_APPLY("/limit/loan/apply/", "放款申请"),
    /**
     * 放款申请状态查询
     */
    LOAN_QUERY("/limit/loan/query/", "放款申请状态查询"),
    /**
     * 还款计划查询
     */
    REPAY_PLAN_QUERY("/repaymentPlan/query/v2/", "还款计划查询"),
    /**
     * 还款试算
     */
    REPAY_TRIAL("/repayment/trial/v2/", "还款试算"),
    /**
     * 还款申请 /xBatch/repayment/apply/{合作方简称}
     */
    REPAY_APPLY("/xBatch/repayment/apply/", "还款申请"),
    /**
     * 还款结果查询
     */
    REPAY_QUERY("/repayment/query/v2/", "还款结果查询"),
    /**
     * 结清证明申请
     */
    SETTLE_VOUCHER_APPLY("/signature/apply/v2/", "结清证明申请"),
    /**
     * 结清证明结果查询
     */
    SETTLE_CERTIFY_QUERY("/signature/query/v2/", "结清证明结果查询"),
    /**
     * LPR查询
     */
    LPR_QUERY("/lpr/query/v2/", "LPR查询"),
    /**
     * 撞库接口
     */
    COLLISION_URL("/limit/checkUser/apply/", "撞库接口"),
    LIMIT_QUERY("/limit/query/","额度查询"),
    LIMIT_ADJUST_APPLY("/forward/limit/adjust/apply/v2/","资产正向调额申请"),
    LIMIT_ADJUST_QUERY("/forward/limit/adjust/query/v2/","资产正向调额结果查询");
    /**
     * 交易码
     */
    private final String tradeCode;

    /**
     * 交易名称
     */
    private final String tradeName;


    CYBKTradeCode(String tradeCode, String tradeName) {
        this.tradeCode = tradeCode;
        this.tradeName = tradeName;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public String getTradeName() {
        return tradeName;
    }
}
