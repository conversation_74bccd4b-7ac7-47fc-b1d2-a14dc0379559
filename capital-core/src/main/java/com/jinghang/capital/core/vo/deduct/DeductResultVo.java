package com.jinghang.capital.core.vo.deduct;



import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.vo.StatusAbleVo;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
public class DeductResultVo extends StatusAbleVo {

    /**
     * 失败描述
     */
    private String failMsg;

    /**
     * 代扣流水号
     */
    private String payOrderNo;

    public boolean isSuccess() {
        return Objects.equals(getStatus(), ProcessStatus.SUCCESS);
    }

    public boolean isProcessing() {
        return Objects.equals(getStatus(), ProcessStatus.PROCESSING);
    }

    public boolean isFail() {
        return Objects.equals(getStatus(), ProcessStatus.FAIL);
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }
}
