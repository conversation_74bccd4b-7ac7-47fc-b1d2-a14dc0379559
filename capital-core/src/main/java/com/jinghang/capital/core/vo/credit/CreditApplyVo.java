package com.jinghang.capital.core.vo.credit;



import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FlowChannel;
import com.jinghang.capital.core.vo.FileInfoVo;
import com.jinghang.capital.core.vo.ProductVo;



import java.math.BigDecimal;
import java.util.List;

public class CreditApplyVo<E> {

    /**
     * 外部授信id
     */
    private String sysId;
    /**
     * 产品
     */
    private ProductVo product;
    /**
     * 渠道
     */
    private FlowChannel flowChannel;
    /**
     * 银行
     */
    private BankChannel bankChannel;

    /**
     * 融担公司
     */
    private GuaranteeCompany guaranteeCompany;
    /**
     * 消费明细
     */
    private String productItem;
    /**
     * 申请金额
     */
    private BigDecimal creditAmt;

    /**
     * 借款金额
     */
    private BigDecimal loanAmt;

    private LoanPurpose loanPurpose;
    /**
     * 申请期数
     */
    private Integer periods;
    /**
     * 对客利率
     */
    private BigDecimal customRate;

    /**
     * 辅助模式
     */
    private String assistMode;

    /**
     * 资产端授信申请合同编号
     */
    private String creditApplyContractNo;

    /**
     * 商户信息
     */
    private MerchantInfoVo merchantInfo;
    /**
     * 用户信息
     */
    private UserInfoVo userInfo;
    /**
     * 身份证信息
     */
    private IdCardInfoVo idCardInfo;
    /**
     * 银行卡信息
     */
    private BankCardInfoVo bankCardInfo;
    /**
     * 协议文件
     */
    private List<FileInfoVo> fileInfoVoList;
    /**
     * 联系人
     */
    private List<UserContactInfoVo> userContactInfoVoList;
    /**
     * 额外信息
     */
    private E extInfo;

    public List<UserContactInfoVo> getUserContactInfoVoList() {
        return userContactInfoVoList;
    }

    public void setUserContactInfoVoList(List<UserContactInfoVo> userContactInfoVoList) {
        this.userContactInfoVoList = userContactInfoVoList;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public ProductVo getProduct() {
        return product;
    }

    public void setProduct(ProductVo product) {
        this.product = product;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getProductItem() {
        return productItem;
    }

    public void setProductItem(String productItem) {
        this.productItem = productItem;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public void setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public BigDecimal getCustomRate() {
        return customRate;
    }

    public void setCustomRate(BigDecimal customRate) {
        this.customRate = customRate;
    }


    public String getAssistMode() {
        return assistMode;
    }

    public void setAssistMode(String assistMode) {
        this.assistMode = assistMode;
    }

    public MerchantInfoVo getMerchantInfo() {
        return merchantInfo;
    }

    public void setMerchantInfo(MerchantInfoVo merchantInfo) {
        this.merchantInfo = merchantInfo;
    }

    public UserInfoVo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfoVo userInfo) {
        this.userInfo = userInfo;
    }

    public IdCardInfoVo getIdCardInfo() {
        return idCardInfo;
    }

    public void setIdCardInfo(IdCardInfoVo idCardInfo) {
        this.idCardInfo = idCardInfo;
    }

    public String getCreditApplyContractNo() {
        return creditApplyContractNo;
    }

    public void setCreditApplyContractNo(String creditApplyContractNo) {
        this.creditApplyContractNo = creditApplyContractNo;
    }

    public BankCardInfoVo getBankCardInfo() {
        return bankCardInfo;
    }

    public void setBankCardInfo(BankCardInfoVo bankCardInfo) {
        this.bankCardInfo = bankCardInfo;
    }

    public List<FileInfoVo> getFileInfoVoList() {
        return fileInfoVoList;
    }

    public void setFileInfoVoList(List<FileInfoVo> fileInfoVoList) {
        this.fileInfoVoList = fileInfoVoList;
    }

    public E getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(E extInfo) {
        this.extInfo = extInfo;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }
}
