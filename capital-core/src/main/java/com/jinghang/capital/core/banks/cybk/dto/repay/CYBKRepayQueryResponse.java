package com.jinghang.capital.core.banks.cybk.dto.repay;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRepayQueryResponse {

    /**
     * 外部还款流水号
     */
    private String outRepaymentSeq;
    /**
     *长银还款流水号
     */
    private String setlSeq;
    /**
     *长银授信流水号
     */
    private String applCde;
    /**
     *长银借据号
     */
    private String loanNo;
    /**
     *还款状态
     */
    private String billStatus;
    /**
     *失败原因
     */
    private String failReason;
    /**
     *还款金额
     */
    private String applyRepayAmt;
    /**
     *实际处理还款金额
     */
    private String dealRepayAmt;
    /**
     *还款时间
     */
    private String crtDt;

    public String getOutRepaymentSeq() {
        return outRepaymentSeq;
    }

    public void setOutRepaymentSeq(String outRepaymentSeq) {
        this.outRepaymentSeq = outRepaymentSeq;
    }

    public String getSetlSeq() {
        return setlSeq;
    }

    public void setSetlSeq(String setlSeq) {
        this.setlSeq = setlSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getApplyRepayAmt() {
        return applyRepayAmt;
    }

    public void setApplyRepayAmt(String applyRepayAmt) {
        this.applyRepayAmt = applyRepayAmt;
    }

    public String getDealRepayAmt() {
        return dealRepayAmt;
    }

    public void setDealRepayAmt(String dealRepayAmt) {
        this.dealRepayAmt = dealRepayAmt;
    }

    public String getCrtDt() {
        return crtDt;
    }

    public void setCrtDt(String crtDt) {
        this.crtDt = crtDt;
    }



    public boolean isSuccess() {
        return Objects.equals(billStatus, "01");
    }

    public boolean isFail() {
        return Objects.equals(billStatus, "02");
    }

    public boolean isProcessing() {
        return Objects.equals(billStatus, "03");
    }
}
