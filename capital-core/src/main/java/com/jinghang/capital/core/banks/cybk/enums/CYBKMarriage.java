package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.core.enums.Marriage;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/27
 */
public enum CYBKMarriage {
    UNMARRIED("10", "未婚", Marriage.UNMARRIED),
    MARRIED("20", "已婚", Marriage.MARRIED),
    WIDOWED("50", "丧偶", Marriage.WIDOWED),
    UNKNOWN("90", "其它", Marriage.UNKNOWN);
    private final String code;
    private final String desc;
    private final Marriage marriage;

    CYBKMarriage(String code, String desc, Marriage marriage) {
        this.code = code;
        this.desc = desc;
        this.marriage = marriage;
    }

    public static CYBKMarriage getEnumByMarriage(Marriage marriage) {
        return Arrays.stream(values()).filter(l -> marriage.equals(l.marriage)).findFirst().orElse(UNKNOWN);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Marriage getMarriage() {
        return marriage;
    }
}
