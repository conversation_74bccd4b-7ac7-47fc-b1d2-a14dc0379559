package com.jinghang.capital.core.banks.cybk.enums;


import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/27
 * 单位所属行业
 */
public enum CYBKIndustry {
    A("农、林、牧、渔业", "FIFTEEN"),
    B("采矿业", "FOURTEEN"),
    C("制造业", "TWELVE"),
    D("电力、热力、燃气及水生产和供应业", "TWO"),
    E("建筑业", "ONE"),
    F("交通运输、仓储和邮政业", "THREE"),
    G("信息传输、软件和信息技术服务业", "NINE"),
    H("批发和零售业", "SEVEN"),
    I("住宿和餐饮业", "SIX"),
    J("金融业", "TEN"),
    K("房地产业", "FIVE"),
    L("租赁和商务服务业", "EIGHT"),
    M("科学研究和技术服务业", "M"),
    N("水利、环境和公共设施管理业", "EIGHTEEN"),
    O("居民服务、修理和其他服务业", "SIXTEEN"),
    P("教育", "FOUR"),
    Q("卫生和社会工作", "SEVENTEEN"),
    R("文化、体育和娱乐业", "ELEVEN"),
    S("公共管理、社会保障和社会组织", "NINETEEN"),
    T("国际组织", "TWENTY");

    private final String desc;
    private final String industry;

    CYBKIndustry(String desc, String industry) {
        this.desc = desc;
        this.industry = industry;
    }

    public static CYBKIndustry getEnumByIndustry(String industry) {
        return Arrays.stream(values()).filter(l -> industry.equals(l.industry)).findFirst().orElse(CYBKIndustry.N);
    }
}
