package com.jinghang.capital.core.banks.cybk.recc.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024-11-12 11:27
 **/
public class CYBKCustDailyLoan {
    private String loanSeq;
    private String applSeq;
    private String outApplSeq;
    private String settleDate;
    private String status;
    private String clearDate;
    /**
     * 五级分类
     * 1正常：[0,1)
     * 2关注：[1,31)
     * 3次级：[31,91)
     * 4可疑：[91,361)
     * 5损失：[361,*****************)"
     */
    private String assetClass;
    /**
     * 下一还款日
     */
    private String nextRepayDate;
    private Integer unpaidPeriods;
    private Integer overduePeriods;
    private String overdueDays;
    private BigDecimal principal;
    private BigDecimal interest;
    private BigDecimal penalty;
    private BigDecimal guarantee;
    private String accrued;
    private String writeOff;

    private String guaranteeContractNo;
    private Integer periods;
    private Date loanTime;

    public Date getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(Date loanTime) {
        this.loanTime = loanTime;
    }
    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public String getGuaranteeContractNo() {
        return guaranteeContractNo;
    }

    public void setGuaranteeContractNo(String guaranteeContractNo) {
        this.guaranteeContractNo = guaranteeContractNo;
    }
    public BigDecimal getGuarantee() {
        return guarantee;
    }

    public void setGuarantee(BigDecimal guarantee) {
        this.guarantee = guarantee;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getApplSeq() {
        return applSeq;
    }

    public void setApplSeq(String applSeq) {
        this.applSeq = applSeq;
    }

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getClearDate() {
        return clearDate;
    }

    public void setClearDate(String clearDate) {
        this.clearDate = clearDate;
    }

    public String getAssetClass() {
        return assetClass;
    }

    public void setAssetClass(String assetClass) {
        this.assetClass = assetClass;
    }

    public String getNextRepayDate() {
        return nextRepayDate;
    }

    public void setNextRepayDate(String nextRepayDate) {
        this.nextRepayDate = nextRepayDate;
    }

    public Integer getUnpaidPeriods() {
        return unpaidPeriods;
    }

    public void setUnpaidPeriods(Integer unpaidPeriods) {
        this.unpaidPeriods = unpaidPeriods;
    }

    public Integer getOverduePeriods() {
        return overduePeriods;
    }

    public void setOverduePeriods(Integer overduePeriods) {
        this.overduePeriods = overduePeriods;
    }

    public String getOverdueDays() {
        return overdueDays;
    }

    public void setOverdueDays(String overdueDays) {
        this.overdueDays = overdueDays;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public String getAccrued() {
        return accrued;
    }

    public void setAccrued(String accrued) {
        this.accrued = accrued;
    }

    public String getWriteOff() {
        return writeOff;
    }

    public void setWriteOff(String writeOff) {
        this.writeOff = writeOff;
    }
}
