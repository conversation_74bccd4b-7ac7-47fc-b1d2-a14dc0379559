package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.RepayService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.repay.*;
import com.jinghang.capital.core.convert.apivo.ActiveLaunchClaimConvert;
import com.jinghang.capital.core.convert.apivo.ApiOverduePlanConvert;
import com.jinghang.capital.core.convert.apivo.ApiPlanConvert;
import com.jinghang.capital.core.convert.apivo.ApiRepayConvert;
import com.jinghang.capital.core.convert.apivo.ClaimMarkConvert;
import com.jinghang.capital.core.convert.apivo.ClaimRetryConvert;
import com.jinghang.capital.core.convert.apivo.RepayDateApplyConvert;
import com.jinghang.capital.core.convert.apivo.RepayNoticeConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MockService;
import com.jinghang.capital.core.vo.repay.*;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("repay")
public class RepayController implements RepayService {
    private static final Logger logger = LoggerFactory.getLogger(RepayController.class);

    private final ManageService manageService;

    @Autowired
    private MockService mockService;

    @Value("${sms.mock.qhyp}")
    private Boolean mock;

    @Autowired
    public RepayController(ManageService manageService) {
        this.manageService = manageService;
    }

    @Override
    public RestResult<TrailResultDto> trial(RepayTrailDto trailDto) {

        logger.info("core repay trial info: {}", JsonUtil.toJsonString(trailDto));

        if (mock) {
            return mockService.trial(trailDto);
        }

        RepayTrailVo trailVo = ApiRepayConvert.INSTANCE.toVo(trailDto);
        TrailResultVo trailResultVo = manageService.repayTrail(trailVo);
        TrailResultDto trailResultDto = ApiRepayConvert.INSTANCE.toDto(trailResultVo);

        logger.info("core repay trial info result: {}", JsonUtil.toJsonString(trailResultDto));
        return RestResult.success(trailResultDto);
    }

    @Override
    public RestResult<RepayResultDto> repay(RepayApplyDto applyDto) {
        logger.info("core repay apply req: {}", JsonUtil.toJsonString(applyDto));

        if (mock) {
            return mockService.repay(applyDto);
        }

        RepayApplyVo repayApplyVo = ApiRepayConvert.INSTANCE.toVo(applyDto);

        PartRepayType partRepayType = repayApplyVo.getPartRepayType();
        if (partRepayType == null) {
            repayApplyVo.setPartRepayType(PartRepayType.NONE);
        }
        //
        RepayResultVo repayResultVo = manageService.repayApply(repayApplyVo);
        RepayResultDto resultDto = ApiRepayConvert.INSTANCE.toDto(repayResultVo);
        logger.info("core repay apply result: {}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<RepayResultDto> queryResult(RepayQueryDto queryDto) {

        logger.info("core repay query req: {}", JsonUtil.toJsonString(queryDto));

        if (mock) {
            return mockService.queryResult(queryDto);
        }

        RepayQueryVo repayQueryVo = ApiRepayConvert.INSTANCE.toVo(queryDto);
        //
        RepayResultVo repayResultVo = manageService.repayQuery(repayQueryVo);
        RepayResultDto resultDto = ApiRepayConvert.INSTANCE.toDto(repayResultVo);

        logger.info("core repay query result: {}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<PlanDto> queryPlan(PlanQueryDto queryDto) {
        if (mock) {
            return mockService.queryPlan(queryDto);
        }
        PlanQueryVo planQueryVo = ApiPlanConvert.INSTANCE.toVo(queryDto);
        //
        PlanVo planQueryResultVo = manageService.queryRepayPlan(planQueryVo);
        PlanDto planDto = ApiPlanConvert.INSTANCE.toDto(planQueryResultVo);
        return RestResult.success(planDto);
    }

    @Override
    public RestResult<OverduePlanDto> queryOverduePlan(OverduePlanQueryDto overduePlanQueryDto) {
        OverduePlanQueryVo planQueryVo = ApiOverduePlanConvert.INSTANCE.toVo(overduePlanQueryDto);
        //
        OverduePlanVo planQueryResultVo = manageService.queryOverduePlan(planQueryVo);
        OverduePlanDto planDto = ApiOverduePlanConvert.INSTANCE.toDto(planQueryResultVo);
        return RestResult.success(planDto);
    }

    @Override
    public RestResult<CompensatedRepaySyncRltDto> compensatedRepaySync(CompensatedRepaySyncDto repaySyncDto) {
        CompensatedRepaySyncVo compensatedRepaySyncVo = ApiRepayConvert.INSTANCE.toVo(repaySyncDto);
        //
        CompensatedRepaySyncRlt compensatedRepaySyncRlt = manageService.compensatedRepaySync(compensatedRepaySyncVo);
        CompensatedRepaySyncRltDto compensatedRepaySyncRltDto = ApiRepayConvert.INSTANCE.toDto(compensatedRepaySyncRlt);
        return RestResult.success(compensatedRepaySyncRltDto);
    }

    /**
     * 代偿标记
     *
     * @param claimMarkApplyDto 代偿标记申请
     * @return 结果
     */
    @Override
    public RestResult<ClaimMarkResultDto> claimMark(ClaimMarkApplyDto claimMarkApplyDto) {
        ClaimMarkApplyVo applyVo = ClaimMarkConvert.INSTANCE.toApplyVo(claimMarkApplyDto);
        ClaimMarkResultVo resultVo = manageService.claimMark(applyVo);
        ClaimMarkResultDto resultDto = ClaimMarkConvert.INSTANCE.toResultDto(resultVo);
        return RestResult.success(resultDto);
    }


    /**
     * 挪息操作
     *
     * @param repayDateApplyDto 资方挪息申请
     * @return 结果
     */
    @Override
    public RestResult<RepayDateResultDto> appropriationInterest(RepayDateApplyDto repayDateApplyDto) {
        RepayDateApplyVo applyVo = RepayDateApplyConvert.INSTANCE.toApplyVo(repayDateApplyDto);
        RepayDateResultVo resultVo = manageService.appropriationInterest(applyVo);
        RepayDateResultDto resultDto = RepayDateApplyConvert.INSTANCE.toResultDto(resultVo);
        return RestResult.success(resultDto);
    }

    /**
     * 代偿重试
     *
     * @param claimRetryDto 代偿重试
     * @return
     */
    @Override
    public RestResult<ClaimRetryResultDto> claimRetry(ClaimRetryDto claimRetryDto) {
        ClaimRetryVo applyVo = ClaimRetryConvert.INSTANCE.toVo(claimRetryDto);
        ClaimRetryResultVo resultVo = manageService.claimRetry(applyVo);
        ClaimRetryResultDto resultDto = ClaimRetryConvert.INSTANCE.toResultDto(resultVo);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<RepayNoticeResultDto> repayNotice(RepayNoticeDto repayNoticeDto) {
        logger.info("还款通知，请求入参：{}", JsonUtil.toJsonString(repayNoticeDto));
        RepayNoticeVo vo = RepayNoticeConvert.INSTANCE.toApplyVo(repayNoticeDto);
        RepayNoticeResultVo result = manageService.repayNotice(vo);
        RepayNoticeResultDto resultDto = RepayNoticeConvert.INSTANCE.toResultDto(result);
        logger.info("还款通知，请求结果：{}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    /**
     * xxl-job调用
     *
     * @param activeLaunchClaimApplyDto 主动发起代偿申请
     * @return
     */
    @Override
    public RestResult<ActiveLaunchClaimResultDto> activeLaunchClaim(ActiveLaunchClaimApplyDto activeLaunchClaimApplyDto) {
        logger.info("主动发起代偿，请求入参：{}", JsonUtil.toJsonString(activeLaunchClaimApplyDto));
        ActiveLaunchClaimApplyVo vo = ActiveLaunchClaimConvert.INSTANCE.toApplyVo(activeLaunchClaimApplyDto);
        ActiveLaunchClaimResultVo result = manageService.activeLaunchClaim(vo);
        ActiveLaunchClaimResultDto resultDto = ActiveLaunchClaimConvert.INSTANCE.toResultDto(result);
        logger.info("主动发起代偿，请求结果：{}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<RepayResultDto> batchApply(RepayDeductionApplyDto repayApply) {
        logger.info("core repay batch apply req: {}", JsonUtil.toJsonString(repayApply));

        RepayDeductionApplyVo repayApplyVo = ApiRepayConvert.INSTANCE.toVo(repayApply);

        RepayResultVo repayResultVo = manageService.batchApply(repayApplyVo);
        RepayResultDto resultDto = ApiRepayConvert.INSTANCE.toDto(repayResultVo);
        logger.info("core repay deduction apply result: {}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<RepayBatchResultDto> batchQuery(RepayQueryDto repayQuery) {
        logger.info("batch repay query req: {}", JsonUtil.toJsonString(repayQuery));

        RepayQueryVo repayQueryVo = ApiRepayConvert.INSTANCE.toVo(repayQuery);

        RepayBatchResultVo repayResultVo = manageService.batchQueryResult(repayQueryVo);
        RepayBatchResultDto resultDto = ApiRepayConvert.INSTANCE.toDto(repayResultVo);

        logger.info("batch repay query result: {}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<BatchTrialResultDto> batchTrial(BatchTrailDto trailDto) {
        logger.info("core batch repay trial info: {}", JsonUtil.toJsonString(trailDto));

        BatchTrailVo trailVo = ApiRepayConvert.INSTANCE.toVo(trailDto);
        BatchTrialResultVo trailResultVos = manageService.batchTrial(trailVo);
        BatchTrialResultDto trailResultDto = ApiRepayConvert.INSTANCE.toDto(trailResultVos);

        logger.info("core batch repay trial info result: {}", JsonUtil.toJsonString(trailResultDto));
        return RestResult.success(trailResultDto);
    }

    @Override
    public RestResult<Void> trustPlanRelease(TrustPlanReleaseDto releaseDto) {
        logger.info("gmxt trust plan release:{}", JsonUtil.toJsonString(releaseDto));
        TrustPlanReleaseVo trustPlanReleaseVo = ApiPlanConvert.INSTANCE.toVo(releaseDto);
        manageService.trustPlanRelease(trustPlanReleaseVo);
        return RestResult.success(null);
    }

    @Override
    public RestResult<DefrayResultDto> defray(DefrayDto defrayDto) {
        logger.info("core repay defray:{}", JsonUtil.toJsonString(defrayDto));
        DefrayVo defrayVo = ApiRepayConvert.INSTANCE.toVo(defrayDto);
        DefrayResultVo resultVo = manageService.defray(defrayVo);
        DefrayResultDto resultDto = ApiRepayConvert.INSTANCE.toDto(resultVo);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<SubstituteMarkResultDto> substituteMark(SubstituteMarkApplyDto substituteMarkApplyDto) {
        logger.info("core substitute mark:{}", JsonUtil.toJsonString(substituteMarkApplyDto));
        SubstituteMarkApplyVo applyVo = ApiRepayConvert.INSTANCE.toVo(substituteMarkApplyDto);
        SubstituteMarkResultVo resultVo = manageService.substituteMark(applyVo);
        SubstituteMarkResultDto resultDto = ApiRepayConvert.INSTANCE.toDto(resultVo);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<SubstituteApplyResultDto> substituteApply(SubstituteApplyDto substituteApplyDto) {
        logger.info("core substitute apply:{}", JsonUtil.toJsonString(substituteApplyDto));
        SubstituteApplyVo applyVo = ApiRepayConvert.INSTANCE.toVo(substituteApplyDto);
        SubstituteApplyResultVo resultVo = manageService.substituteApply(applyVo);
        SubstituteApplyResultDto resultDto = ApiRepayConvert.INSTANCE.toDto(resultVo);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<SubstituteApplyResultDto> handSubstituteApply(SubstituteApplyDto substituteApplyDto) {
        logger.info("core handSubstitute apply:{}", JsonUtil.toJsonString(substituteApplyDto));
        SubstituteApplyVo applyVo = ApiRepayConvert.INSTANCE.toVo(substituteApplyDto);
        SubstituteApplyResultVo resultVo = manageService.handSubstituteApply(applyVo);
        SubstituteApplyResultDto resultDto = ApiRepayConvert.INSTANCE.toDto(resultVo);
        return RestResult.success(resultDto);
    }


    /**
     * 线下还款回盘文件上传sftp
     */
    @PostMapping("offlineReturnFile/upload")
    public RestResult<RepayReturnUploadResultDto> repayOfflineReturnFileUpload(@RequestBody RepayReturnUploadDto uploadDto){
        logger.info("core offlineReturnFile/upload request:{}", JsonUtil.toJsonString(uploadDto));
        RepayReturnUploadVo applyVo = ApiRepayConvert.INSTANCE.toVo(uploadDto);
        RepayReturnUploadResultDto result = manageService.handleOfflineRepayReturnFileUpload(applyVo);

        return RestResult.success(result);
    }








}
