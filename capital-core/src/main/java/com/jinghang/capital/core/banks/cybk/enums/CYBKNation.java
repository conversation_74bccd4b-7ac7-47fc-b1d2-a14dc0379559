package com.jinghang.capital.core.banks.cybk.enums;

/**
 * 民族
 */
public enum CYBKNation {
    HAN("00", "汉"),
    MENG_GU("01", "蒙古"),
    HUI("02", "回"),
    ZANG("03", "藏"),
    WEI_WU_ER("04", "维吾尔"),
    MIAO("05", "苗"),
    YI("06", "彝"),
    ZHUANG("07", "壮"),
    BU_YI("08", "布依"),
    CAO_XIAN("09", "朝鲜"),
    MAN("10", "满"),
    DONG("11", "侗"),
    YAO("12", "瑶"),
    BAI("13", "白"),
    TU_JIA("14", "土家"),
    HA_NI("15", "哈尼"),
    HA_SHA_KE("16", "哈萨克"),
    DAI("17", "傣"),
    LI("18", "黎"),
    SU_SU("19", "傈僳"),
    WA("20", "佤"),
    SHE("21", "畲"),
    GAO_SHAN("22", "高山"),
    LA_GU("23", "拉祜"),
    SHUI("24", "水"),
    DONG_XIANG("25", "东乡"),
    NA_XI("26", "纳西"),
    JIN_PIN("27", "景颇"),
    KE_ER_KE_CHU("28", "柯尔克孜"),
    TU("29", "土"),
    DA_KAN_ER("30", "达斡尔"),
    MO_LAO("31", "仫佬"),
    QIANG("32", "羌"),
    BU_LANG("33", "布朗"),
    SA_LA("34", "撒拉"),
    MAO_NAN("35", "毛南"),
    QI_LAO("36", "仡佬"),
    XI_BO("37", "锡伯"),
    A_CHANG("38", "阿昌"),
    PU_MI("39", "普米"),
    TA_JI_KE("40", "塔吉克"),
    NV("41", "怒"),
    WU_ZHI_BIE_KE("42", "乌孜别克"),
    ER_LUO_SI("43", "俄罗斯"),
    E_WEN_KE("44", "鄂温克"),
    DE_ANG("45", "德昂"),
    BAO_AN("46", "保安"),
    YU_GU("47", "裕固"),
    JING("48", "京"),
    TA_TA_ER("49", "塔塔尔"),
    DU_LONG("50", "独龙"),
    E_LUN_CHUN("51", "鄂伦春"),
    HE_ZHE("52", "赫哲"),
    MEN_BA("53", "门巴"),
    LUO_BA("54", "珞巴"),
    JI_NUO("55", "基诺"),
    OTHER("56", "其它"),
    FOREIGN("57", "外国血统中国籍人士");


    /**
     * 码值
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    public static CYBKNation findByName(String nation) {
        CYBKNation[] nations = values();
        for (CYBKNation na : nations) {
            if (na.getDesc().equals(nation)) {
                return na;
            }
        }
        return CYBKNation.OTHER;
    }


    CYBKNation(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
