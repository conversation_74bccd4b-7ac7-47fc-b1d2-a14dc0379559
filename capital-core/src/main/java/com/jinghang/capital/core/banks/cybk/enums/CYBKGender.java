package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.core.enums.Gender;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/28
 */
public enum CYBKGender {
    ONE("10", "男", Gender.MALE),
    TWO("20", "女", Gender.FEMALE);
    private final String code;
    private final String desc;
    private final Gender gender;

    CYBKGender(String code, String desc, Gender gender) {
        this.code = code;
        this.desc = desc;
        this.gender = gender;
    }

    public static String getCodeByGender(Gender gender) {
        return Arrays.stream(values()).filter(l -> gender.equals(l.gender)).findFirst().orElseThrow().code;
    }
}
