package com.jinghang.capital.core.banks.cybk.dto.file;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


/**
 * 4.19 接口
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKClearVoucherQueryRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.SETTLE_CERTIFY_QUERY;



    /**
     * 业务模式
     * 1：非额度类
     * 2：额度类
     */
    private String bizMode;
    /**
     * 合作方流水号
     * 额度类：放款流水号(outLoanSeq)
     * 非额度类：业务流水号(outApplSeq)
     */
    private String outSeq;
    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 身份证号码
     */
    private String idNo;

    public String getBizMode() {
        return bizMode;
    }

    public void setBizMode(String bizMode) {
        this.bizMode = bizMode;
    }

    public String getOutSeq() {
        return outSeq;
    }

    public void setOutSeq(String outSeq) {
        this.outSeq = outSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }
}
