package com.jinghang.capital.core.banks.cybk.dto.repay;

import java.math.BigDecimal;
import java.util.List;

public class RepayInfoRequest   {
    //长银借据号
    private String dealRefNo;
    //期次
    private List<String> termList;
    //还款类型
    private String repayType;
    //长银还款金额
    private BigDecimal repayAmount;
    //对客扣款金额(资产方)
    private BigDecimal custCutAmt;
    //担保还款金额(担保方)
    private BigDecimal guaRepayAmt;
    //担保费罚息金额
    private BigDecimal guaRepayOdAmt;


    public String getDealRefNo() {
        return dealRefNo;
    }

    public void setDealRefNo(String dealRefNo) {
        this.dealRefNo = dealRefNo;
    }

    public List<String> getTermList() {
        return termList;
    }

    public void setTermList(List<String> termList) {
        this.termList = termList;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getCustCutAmt() {
        return custCutAmt;
    }

    public void setCustCutAmt(BigDecimal custCutAmt) {
        this.custCutAmt = custCutAmt;
    }

    public BigDecimal getGuaRepayAmt() {
        return guaRepayAmt;
    }

    public void setGuaRepayAmt(BigDecimal guaRepayAmt) {
        this.guaRepayAmt = guaRepayAmt;
    }

    public BigDecimal getGuaRepayOdAmt() {
        return guaRepayOdAmt;
    }

    public void setGuaRepayOdAmt(BigDecimal guaRepayOdAmt) {
        this.guaRepayOdAmt = guaRepayOdAmt;
    }

}
