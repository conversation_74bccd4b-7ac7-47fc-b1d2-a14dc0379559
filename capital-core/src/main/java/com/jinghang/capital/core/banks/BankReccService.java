package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.vo.recc.ReccApplyVo;
import com.jinghang.capital.core.vo.recc.ReccDownloadVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;

public interface BankReccService extends ChannelSupport {

    void process(ReccApplyVo apply);

    ReccResultVo query(ReccApplyVo apply);

    ReccResultVo download(ReccDownloadVo vo);

}
