package com.jinghang.capital.core.banks.cybk.dto.bind;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKBindCardApplyRequest extends CYBKBaseRequest {

    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.BIND_CARD_APPLY;


    /**
     * 外部申请流水号
     */
    private String outSignSeq;
    /**
     * 支付模式
     */
    private String payMode;
    /**
     * 扣款账户
     */
    private String acctNo;
    /**
     * 扣款账户户名
     */
    private String acctName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码
     */
    private String idNo;
    /**
     * 扣款账户账户类型
     */
    private String acctType;
    /**
     * 手机号
     */
    private String telNo;
    /**
     * 签约渠道
     */
    private String payChannel;
    /**
     * 扣款账户开户行编码
     */
    private String acctBankCode;
    /**
     * 扣款协议号
     */
    private String signNo;
    /**
     * 扣款协议状态
     */
    private String signSts;

    public String getOutSignSeq() {
        return outSignSeq;
    }

    public void setOutSignSeq(String outSignSeq) {
        this.outSignSeq = outSignSeq;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getAcctType() {
        return acctType;
    }

    public void setAcctType(String acctType) {
        this.acctType = acctType;
    }

    public String getTelNo() {
        return telNo;
    }

    public void setTelNo(String telNo) {
        this.telNo = telNo;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getAcctBankCode() {
        return acctBankCode;
    }

    public void setAcctBankCode(String acctBankCode) {
        this.acctBankCode = acctBankCode;
    }

    public String getSignNo() {
        return signNo;
    }

    public void setSignNo(String signNo) {
        this.signNo = signNo;
    }

    public String getSignSts() {
        return signSts;
    }

    public void setSignSts(String signSts) {
        this.signSts = signSts;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }
}
