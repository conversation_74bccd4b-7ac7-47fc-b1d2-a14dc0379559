package com.jinghang.capital.core.banks.cybk.dto.credit;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/10/10 16:55
 **/
public class CYBKJobInfo {

    /**
     * 工作性质
     */
    private String positionOpt;
    /**
     * 职业
     */
    private String profession;
    /**
     * 现任职务
     */
    private String indivPosition;
    /**
     * 职务为其他备注
     */
    private String positionRmk;
    /**
     * 职称
     */
    private String indivProfsn;
    /**
     * 所属行业
     */
    private String belongsIndus;
    /**
     * 现单位名称
     */
    private String indivEmpName;
    /**
     * 单位地址
     */
    private CYBKAddress indivEmpAddInfo;
    /**
     * 单位性质
     */
    private String indivEmpTyp;
    /**
     * 单位邮编
     */
    private String indivEmpZip;
    /**
     * 单位电话
     */
    private String indivEmpTel;
    /**
     * 本单位工作起始年份
     */
    private String indivSttWork;
    /**
     * 就业状况
     */
    private String obtainWork;

    public String getPositionOpt() {
        return positionOpt;
    }

    public void setPositionOpt(String positionOpt) {
        this.positionOpt = positionOpt;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getIndivPosition() {
        return indivPosition;
    }

    public void setIndivPosition(String indivPosition) {
        this.indivPosition = indivPosition;
    }

    public String getPositionRmk() {
        return positionRmk;
    }

    public void setPositionRmk(String positionRmk) {
        this.positionRmk = positionRmk;
    }

    public String getIndivProfsn() {
        return indivProfsn;
    }

    public void setIndivProfsn(String indivProfsn) {
        this.indivProfsn = indivProfsn;
    }

    public String getBelongsIndus() {
        return belongsIndus;
    }

    public void setBelongsIndus(String belongsIndus) {
        this.belongsIndus = belongsIndus;
    }

    public String getIndivEmpName() {
        return indivEmpName;
    }

    public void setIndivEmpName(String indivEmpName) {
        this.indivEmpName = indivEmpName;
    }

    public CYBKAddress getIndivEmpAddInfo() {
        return indivEmpAddInfo;
    }

    public void setIndivEmpAddInfo(CYBKAddress indivEmpAddInfo) {
        this.indivEmpAddInfo = indivEmpAddInfo;
    }

    public String getIndivEmpTyp() {
        return indivEmpTyp;
    }

    public void setIndivEmpTyp(String indivEmpTyp) {
        this.indivEmpTyp = indivEmpTyp;
    }

    public String getIndivEmpZip() {
        return indivEmpZip;
    }

    public void setIndivEmpZip(String indivEmpZip) {
        this.indivEmpZip = indivEmpZip;
    }

    public String getIndivEmpTel() {
        return indivEmpTel;
    }

    public void setIndivEmpTel(String indivEmpTel) {
        this.indivEmpTel = indivEmpTel;
    }

    public String getIndivSttWork() {
        return indivSttWork;
    }

    public void setIndivSttWork(String indivSttWork) {
        this.indivSttWork = indivSttWork;
    }

    public String getObtainWork() {
        return obtainWork;
    }

    public void setObtainWork(String obtainWork) {
        this.obtainWork = obtainWork;
    }
}
