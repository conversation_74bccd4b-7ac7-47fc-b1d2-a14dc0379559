package com.jinghang.capital.core.banks.cybk.service;

import com.jinghang.capital.core.banks.AbstractBankRepayService;
import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.banks.cybk.dto.repay.*;
import com.jinghang.capital.core.banks.cybk.remote.CYBKRequestService;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.*;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.BankRepayRecordRepository;
import com.jinghang.capital.core.repository.CYBKBankRepository;
import com.jinghang.capital.core.repository.CYBKCreditFlowRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.service.MockService;
import com.jinghang.capital.core.service.credit.FinCreditService;
import com.jinghang.capital.core.util.AmountUtil;
import com.jinghang.capital.core.vo.repay.*;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Service
@Qualifier("CYBKRepayService")
public class CYBKRepayService extends AbstractBankRepayService {

    private static final Logger logger = LoggerFactory.getLogger(CYBKRepayService.class);
    private static final int TWO = 2;
    private static final int PROCESSING_POINT_NUM = 6;
    private static final int YEAR_DAYS = 360;
    private final Integer repayQueryDdlTime = 120000;
    @Autowired
    private CYBKRequestService cybkRequestService;
    @Autowired
    private FinCreditService finCreditService;
    @Autowired
    private CYBKConfig config;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private CYBKBankRepository cybkBankRepository;
    @Autowired
    private CYBKCreditFlowRepository cybkCreditFlowRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;
    @Autowired
    private MockService mockService;

    @Override
    public RepayResultVo onlineRepay(CustomerRepayRecord customerRepayRecord, LoanReplan repayPlan, RepayApplyVo repayApplyDto) {
        String loanId = repayPlan.getLoanId();
        Integer period = repayPlan.getPeriod();

        //代偿后还款
        if (getFinRepayService().existClaimedRepayPlan(loanId, period)) {
            // 对客还款计划更新成功
            doCustomReplanSuccess(customerRepayRecord.getId());

            if (customerRepayRecord.getRepayPurpose() == RepayPurpose.CLEAR) {
                //提前结清,更新剩余期数对客状态为成功
                modifyClearCustomStatus(customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod());
            }
            //对资罚息
            customerRepayRecord.setBankPenaltyAmt(repayPlan.getPenaltyAmt());

            //对资已代偿,不需要接口通知,直接返回成功
            return repayAfterClaim();
        }
        // 对资代偿前还款, 正常还款
        RepayTrailVo repayTrailVo = new RepayTrailVo();
        repayTrailVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        repayTrailVo.setLoanId(loanId);
        repayTrailVo.setPeriod(period);

        //还款前 调资方试算
        TrailResultVo trailResult = trail(repayTrailVo);

        //初始化 对资还款记录
        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setPrincipalAmt(trailResult.getPrincipal());
        bankRepayRecord.setInterestAmt(trailResult.getInterest());
        bankRepayRecord.setGuaranteeAmt(trailResult.getGuaranteeFee());
        bankRepayRecord.setPenaltyAmt(trailResult.getOverdueFee()); //对资应还罚息
        bankRepayRecord.setBreachAmt(trailResult.getBreachFee()); //应还违约金
        bankRepayRecord.setTotalAmt(
                sumAmount(bankRepayRecord.getPrincipalAmt(),
                        bankRepayRecord.getInterestAmt(),
                        bankRepayRecord.getGuaranteeAmt(),
                        bankRepayRecord.getPenaltyAmt(),
                        bankRepayRecord.getBreachAmt()));
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setSysId(customerRepayRecord.getId());
        bankRepayRecord.setLoanId(loanId);
        bankRepayRecord.setPeriod(period);
        bankRepayRecord.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        bankRepayRecord.setRepayMode(customerRepayRecord.getRepayMode());
        bankRepayRecord.setRepayType(customerRepayRecord.getRepayType());
        bankRepayRecord.setChannel(customerRepayRecord.getChannel());
        bankRepayRecord.setRepayStatus(ProcessStatus.INIT);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setChargeChannelId(customerRepayRecord.getChargeChannelId());
        bankRepayRecord.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        bankRepayRecord = getFinRepayService().saveBankRepayRecord(bankRepayRecord);

        RepayApplyVo applyVo = new RepayApplyVo();
        applyVo.setLoanId(loanId);
        applyVo.setPeriod(period);
        applyVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        applyVo.setRepayMode(customerRepayRecord.getRepayMode());
        applyVo.setRepayType(customerRepayRecord.getRepayType());

        //保存还款卡信息
        bankRepayRecord.setAgreementNo(customerRepayRecord.getAgreementNo());
        bankRepayRecord.setRepayBankCode(customerRepayRecord.getRepayBankCode());
        bankRepayRecord.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        bankRepayRecord.setRepayRelUser(customerRepayRecord.getRepayRelUser());
        bankRepayRecord.setRepayRelPhone(customerRepayRecord.getRepayRelPhone());
        bankRepayRecord.setRepayRelCard(customerRepayRecord.getRepayRelCard());
        bankRepayRecord.setPayOrderNo(customerRepayRecord.getPayOrderNo());
        // 拼装银行卡信息
        String repayBankAbbr = customerRepayRecord.getRepayBankCode();
        CYBKBankList cybkBankList = cybkBankRepository.findByAbbrIgnoreCase(repayBankAbbr);
        if (cybkBankList != null) {
            applyVo.setRepayBankName(cybkBankList.getName());
        }
        applyVo.setRepayRelUser(customerRepayRecord.getRepayRelUser());
        applyVo.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        applyVo.setRepayBankCode(repayBankAbbr);
        applyVo.setAgreementNo(customerRepayRecord.getAgreementNo());
        // 调用资方
        return bankRepayApply(applyVo, bankRepayRecord, customerRepayRecord.getReduceAmt(), customerRepayRecord.getId());
    }

    @Override
    public RepayResultVo offlineRepay(CustomerRepayRecord customerRepayRecord, LoanReplan replan, RepayApplyVo repayApplyDto) {

        logger.info("长银直连线下还款销账:loanId:{},period:{}", replan.getLoanId(), replan.getPeriod());
        // 对客还款计划更新成功
        doCustomReplanSuccess(customerRepayRecord.getId());

        // 对资已代偿,不需要接口通知,直接返回成功
        if (RepayStatus.REPAID.equals(replan.getBankRepayStatus())) {

            if (customerRepayRecord.getRepayPurpose() == RepayPurpose.CLEAR) {
                //线下提前结清,更新剩余期数对客状态为成功
                modifyClearCustomStatus(customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod());
            }

            //对资罚息
            customerRepayRecord.setBankPenaltyAmt(replan.getPenaltyAmt());

            //对资已代偿,不需要接口通知,直接返回成功
            return repayAfterClaim();
        }

        RepayResultVo repayResultVo = new RepayResultVo();
        repayResultVo.setStatus(ProcessStatus.SUCCESS);

        String loanId = replan.getLoanId();
        Integer period = replan.getPeriod();

        RepayResultVo bankRepayResult;

        RepayApplyVo applyVo = new RepayApplyVo();
        applyVo.setLoanId(loanId);
        applyVo.setPeriod(period);
        applyVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        applyVo.setRepayMode(customerRepayRecord.getRepayMode());
        applyVo.setRepayType(customerRepayRecord.getRepayType());
        applyVo.setTransferDate(customerRepayRecord.getTransferDate());
        RepayTrailVo repayTrailVo = new RepayTrailVo();
        repayTrailVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        repayTrailVo.setLoanId(loanId);
        repayTrailVo.setPeriod(period);
        repayTrailVo.setTransferDate(customerRepayRecord.getTransferDate());
        // 试算
        TrailResultVo trailResult = directTrail(repayTrailVo);

        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setPrincipalAmt(trailResult.getPrincipal());
        bankRepayRecord.setInterestAmt(trailResult.getInterest());
        bankRepayRecord.setGuaranteeAmt(trailResult.getGuaranteeFee());
        bankRepayRecord.setPenaltyAmt(trailResult.getOverdueFee());
        bankRepayRecord.setBreachAmt(BigDecimal.ZERO);
        bankRepayRecord.setTotalAmt(
                sumAmount(
                        bankRepayRecord.getPrincipalAmt(),
                        bankRepayRecord.getInterestAmt(),
                        bankRepayRecord.getGuaranteeAmt(),
                        bankRepayRecord.getPenaltyAmt(),
                        bankRepayRecord.getBreachAmt()));
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setSysId(customerRepayRecord.getId());
        bankRepayRecord.setLoanId(loanId);
        bankRepayRecord.setPeriod(period);
        bankRepayRecord.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        bankRepayRecord.setRepayMode(customerRepayRecord.getRepayMode());
        bankRepayRecord.setRepayType(customerRepayRecord.getRepayType());
        bankRepayRecord.setChannel(customerRepayRecord.getChannel());
        bankRepayRecord.setRepayStatus(ProcessStatus.INIT);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setReduceAmount(customerRepayRecord.getReduceAmt());


        //保存还款卡信息
        bankRepayRecord.setAgreementNo(customerRepayRecord.getAgreementNo());
        bankRepayRecord.setRepayBankCode(customerRepayRecord.getRepayBankCode());
        bankRepayRecord.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        bankRepayRecord.setRepayRelUser(customerRepayRecord.getRepayRelUser());
        bankRepayRecord.setRepayRelPhone(customerRepayRecord.getRepayRelPhone());
        bankRepayRecord.setRepayRelCard(customerRepayRecord.getRepayRelCard());

        bankRepayRecord = getFinRepayService().saveBankRepayRecord(bankRepayRecord);
        // 还款通知
        bankRepayResult = bankRepayApply(applyVo, bankRepayRecord, customerRepayRecord.getReduceAmt(), customerRepayRecord.getId());
        if (bankRepayResult.getStatus() == ProcessStatus.FAIL) {
            getWarningService().warn("线下还款，通知长银直连失败. loanId: " + loanId + ", period: " + period);
        }

        return repayResultVo;
    }

    /**
     * 代偿后还款
     *
     * @return
     */
    public RepayResultVo repayAfterClaim() {
        // 直接成功
        RepayResultVo resultVo = new RepayResultVo();
        resultVo.setStatus(ProcessStatus.SUCCESS);
        resultVo.setActRepayTime(LocalDateTime.now());
        return resultVo;
    }

    @Override
    public RepayResultVo bankRepayApply(RepayApplyVo repayApplyDto, BankRepayRecord bankRepayRecord, BigDecimal reduceAmt, String custRepayRecordId) {
        RepayResultVo result;
        String loanId = repayApplyDto.getLoanId();
        Loan loan = getFinRepayService().getLoan(loanId, null);

        LoanReplan originPlan = getFinRepayService().getRepayPlan(loanId, repayApplyDto.getPeriod());

        if (originPlan.getBankRepayStatus() == RepayStatus.REPAID) {

            // 对资已代偿,不需要接口通知,直接返回成功
            getWarningService().warn("长银直连 代偿后不需要通知资方 customerRepayRecordId:" + custRepayRecordId);
            result = new RepayResultVo();
            result.setStatus(ProcessStatus.FAIL);
            return result;
        }

        if (bankRepayRecord == null) {
            throw new BizException(BizErrorCode.BANK_REPAY_NOT_FOUND);
        }

        RepayPurpose repayPurpose = repayApplyDto.getRepayPurpose();
        if (repayPurpose == RepayPurpose.CURRENT) {
            result = repayNormal(loan, bankRepayRecord, originPlan, repayApplyDto.getTransferDate(), reduceAmt);
        } else {
            result = repayClear(loan, bankRepayRecord, originPlan, repayApplyDto.getTransferDate(), reduceAmt);
        }

        //以查询结果为终态
        bankRepayRecord.setRepayStatus(ProcessStatus.PROCESSING);
        bankRepayRecord.setBankSerial(result.getBankSeq());
        getFinRepayService().updateBankRepayRecord(bankRepayRecord);

        getMqService().submitRepayQueryDelay(bankRepayRecord.getId(), null, repayQueryDdlTime);
        return result;
    }

    @Override
    public RepayResultVo bankQuery(String repayId) {
        BankRepayRecord repayRecord = getFinRepayService().getBankRepayRecord(repayId);
        Loan loan = loanRepository.findById(repayRecord.getLoanId()).orElseThrow();
        CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();

        CYBKRepayQueryRequest req = new CYBKRepayQueryRequest();
        req.setSetlSeq(repayRecord.getBankSerial());
        req.setOutRepaymentSeq(repayRecord.getId());
        req.setApplCde(creditFlow.getCreditNo());
        req.setLoanNo(creditFlow.getLoanNo());
        logger.info("长银直连还款结果查询请求={}", JsonUtil.toJsonString(req));
        CYBKRepayQueryResponse resp = cybkRequestService.repayQuery(req);
        logger.info("长银直连还款结果查询结果={}", JsonUtil.toJsonString(resp));

        RepayResultVo resultVo = new RepayResultVo();
        resultVo.setBankSeq(resp.getSetlSeq());
        if (resp.isSuccess()) {
            resultVo.setStatus(ProcessStatus.SUCCESS);
            resultVo.setActRepayTime(LocalDateTime.now());
        } else if (resp.isProcessing()) {
            resultVo.setStatus(ProcessStatus.PROCESSING);
        } else if (resp.isFail()) {
            if (RepayMode.OFFLINE.equals(repayRecord.getRepayMode())) {
                getWarningService().warn("长银直联 线下还款失败 bankRepayRecordId: " + repayRecord.getId());
            }
            resultVo.setStatus(ProcessStatus.FAIL);
            resultVo.setFailMsg(resp.getFailReason());
        } else {
            getWarningService().warn("长银直连还款结果查询结果异常: 资方返回状态不正确:" + resp.getBillStatus() + ", BankRepayRecord ID:" + repayId);
            resultVo.setStatus(ProcessStatus.PROCESSING);
        }
        return resultVo;
    }

    @Override
    public CompensatedRepaySyncRlt bankCompensatedRepaySync(CompensatedRepaySyncVo syncVo) {
        return null;
    }

    @Override
    public TrailResultVo trail(RepayTrailVo repayTrailVo) {
        Integer period = repayTrailVo.getPeriod();

        RepayPurpose repayPurpose = repayTrailVo.getRepayPurpose();
        Loan loan = getFinRepayService().getLoan(repayTrailVo.getLoanId(), repayTrailVo.getOuterLoanId());
        if (repayPurpose == RepayPurpose.CURRENT) {
            return trialCurrent(loan, period, repayTrailVo.getTransferDate());
        }
        return trialClear(loan, period, repayTrailVo.getTransferDate());
    }

    /**
     * CORE内部使用
     *
     * @param trailVo
     * @return
     */
    public TrailResultVo directTrail(RepayTrailVo trailVo) {

        Loan loan = getFinRepayService().getLoan(trailVo.getLoanId(), trailVo.getOuterLoanId());
        LoanReplan curPlan = getFinRepayService().getRepayPlan(loan.getId(), trailVo.getPeriod());

        if (trailVo.getRepayPurpose().equals(RepayPurpose.CURRENT)) {
            // 当期4
            return directCurTrail(loan, curPlan, trailVo.getTransferDate());
        }
        //  结清3
        return directClearTrail(loan, curPlan, trailVo.getTransferDate());
    }

    //@Override
    //public void asyncClaimApply(List<BankLoanReplan> results, ClaimMarkApplyVo applyVo) {
    //    // 调用资方试算接口,获取罚息
    //    if (!CollectionUtils.isEmpty(results)) {
    //        for (BankLoanReplan bankLoanReplan : results) {
    //            String loanId = bankLoanReplan.getLoanId();
    //            Integer period = bankLoanReplan.getPeriod();
    //            LoanReplan loanReplan = getFinRepayService().findRepayPlanByLoanIdAndPeriod(loanId, period);

    //            Loan loan = loanRepository.findById(loanId).orElseThrow();
    //            try {

    //                //回购时 试算按结清算,否则罚息金额只有当期的罚息
    //                TrailResultVo trailResult = getRemoteTrailResult(loan, loanReplan, bankLoanReplan.getRepayPurpose());
    //                //罚息大于0 更新还款计划
    //                if (trailResult.getOverdueFee().compareTo(BigDecimal.ZERO) > 0) {
    //                    loanReplan.setPenaltyAmt(trailResult.getOverdueFee());
    //                    loanReplan.setBreachAmt(loanReplan.getBreachFee());
    //                    loanReplan.setTotalAmt(loanReplan.getTotalAmt().add(loanReplan.getPenaltyAmt()).add(loanReplan.getBreachAmt()));
    //                    getFinRepayService().updateRepayPlan(loanReplan);
    //                }
    //            } catch (BizException e) {
    //                getWarningService().warn("长银直连 获取罚息时试算接口异常, loanId:" + loanId + ", period:" + period + ", msg:" + e.getMessage());
    //            }
    //        }
    //    }
    //}

    @Override
    public void changeOnLoan(String loanId, LoanStage stage, BigDecimal amount) {
        getCommonService().changeOnLoan(loanId, LoanStage.AFTER_LOAN, amount);
    }

    @Override
    public void bankRepayApplyMq(String bankRepayRecordId) {
    }

    @Override
    public void bankRepayNotify(CustomerRepayRecord customerRepayRecord) {
        LoanReplan repayPlan = getFinRepayService().getRepayPlan(customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod());

        logger.info("长银直连 重推线下还款销账:loanId:{},period:{}", repayPlan.getLoanId(), repayPlan.getPeriod());

        if (RepayMode.ONLINE.equals(customerRepayRecord.getRepayMode())) {
            getWarningService().warn("长银直连 不允许重推线上还款 customerRepayRecordId:" + customerRepayRecord.getId());
            return;
        }

        if (RepayStatus.REPAID.equals(repayPlan.getBankRepayStatus())) {
            // 对资已代偿,不需要接口通知,直接返回成功
            getWarningService().warn("长银直连 代偿后不需要通知资方 customerRepayRecordId:" + customerRepayRecord.getId());
            return;
        }

        String loanId = repayPlan.getLoanId();
        Integer period = repayPlan.getPeriod();

        RepayResultVo bankRepayResult;

        RepayApplyVo applyVo = new RepayApplyVo();
        applyVo.setLoanId(loanId);
        applyVo.setPeriod(period);
        applyVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        applyVo.setRepayMode(customerRepayRecord.getRepayMode());
        applyVo.setRepayType(customerRepayRecord.getRepayType());
        applyVo.setTransferDate(customerRepayRecord.getTransferDate());
        RepayTrailVo repayTrailVo = new RepayTrailVo();
        repayTrailVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        repayTrailVo.setLoanId(loanId);
        repayTrailVo.setPeriod(period);
        repayTrailVo.setTransferDate(customerRepayRecord.getTransferDate());
        // 试算
        TrailResultVo trailResult = directTrail(repayTrailVo);

        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setPrincipalAmt(trailResult.getPrincipal());
        bankRepayRecord.setInterestAmt(trailResult.getInterest());
        bankRepayRecord.setGuaranteeAmt(trailResult.getGuaranteeFee());
        bankRepayRecord.setTotalAmt(
                sumAmount(
                        bankRepayRecord.getPrincipalAmt(),
                        bankRepayRecord.getInterestAmt(),
                        bankRepayRecord.getGuaranteeAmt(),
                        bankRepayRecord.getPenaltyAmt(),
                        bankRepayRecord.getBreachAmt()));
        bankRepayRecord.setPenaltyAmt(trailResult.getOverdueFee());
        bankRepayRecord.setBreachAmt(BigDecimal.ZERO);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setSysId(customerRepayRecord.getId());
        bankRepayRecord.setLoanId(loanId);
        bankRepayRecord.setPeriod(period);
        bankRepayRecord.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        bankRepayRecord.setRepayMode(customerRepayRecord.getRepayMode());
        bankRepayRecord.setRepayType(customerRepayRecord.getRepayType());
        bankRepayRecord.setChannel(customerRepayRecord.getChannel());
        bankRepayRecord.setRepayStatus(ProcessStatus.INIT);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setReduceAmount(customerRepayRecord.getReduceAmt());
        bankRepayRecord = getFinRepayService().saveBankRepayRecord(bankRepayRecord);


        //保存还款卡信息
        bankRepayRecord.setAgreementNo(customerRepayRecord.getAgreementNo());
        bankRepayRecord.setRepayBankCode(customerRepayRecord.getRepayBankCode());
        bankRepayRecord.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        bankRepayRecord.setRepayRelUser(customerRepayRecord.getRepayRelUser());
        bankRepayRecord.setRepayRelPhone(customerRepayRecord.getRepayRelPhone());
        bankRepayRecord.setRepayRelCard(customerRepayRecord.getRepayRelCard());
        // 还款通知
        bankRepayResult = bankRepayApply(applyVo, bankRepayRecord, customerRepayRecord.getReduceAmt(), customerRepayRecord.getId());

        if (bankRepayResult.getStatus() == ProcessStatus.FAIL) {
            getWarningService().warn("线下还款，通知长银直连失败. loanId: " + loanId + ", period: " + period);
        }
    }

    /**
     * 封装还款参数
     */
    private CYBKRepayApplyRequest buildRepayParam(Loan loan, CYBKRepayParamDTO repayParamDTO, BankRepayRecord bankRepayRecord) {
        Credit credit = finCreditService.getCredit(loan.getCreditId());
        CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(credit.getId()).orElseThrow();
        String repayDate = repayParamDTO.getTransferDate() == null ? LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE)
                : repayParamDTO.getTransferDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
        CYBKRepayApplyRequest req = new CYBKRepayApplyRequest();
        req.setOutRepaySeq(repayParamDTO.getRepayRecordId());
        req.setFinanceChannel("01");
        req.setTerminalType(config.getMerchantTerminal());
        req.setMerchantNo(config.getMerchantCode());
        req.setStoreCode(config.getMerchantShop());
        //是否已到账:线上还款 传N,  线下还款 传Y
        req.setPaymInd(repayParamDTO.getRepayMode());
        //还款渠道:线上还款 传【BF_DFS】,  线下还款 传【XX】
        if ("Y".equals(repayParamDTO.getRepayMode())) {
            req.setPayChannel("XX");
        } else {
            req.setPayChannel(config.getRepayChannel());
        }
        // 实际还款日期 mock time
        req.setRepayDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(mockService.getServerDateTime(repayDate)));
        req.setNeedAct("Y");
        // 还款金额(长银) = 应还本金+应还利息+应还资方罚息
        req.setAmount(repayParamDTO.getTotalPrincipal().add(repayParamDTO.getTotalInterest()).add(repayParamDTO.getTotalPenalty()));
        // 对客扣款金额(资产方) = 应还本金+应还利息+应还资方罚息+应还融担费
        req.setCustCutAmt(repayParamDTO.getTotalPrincipal().add(repayParamDTO.getTotalInterest()).add(repayParamDTO.getTotalPenalty())
                .add(repayParamDTO.getGuaranteeAmt()));
        //担保还款金额(担保方) = 应还融担费
        req.setGuaRepayAmt(repayParamDTO.getGuaranteeAmt());
        List<RepayInfoRequest> repayInfoList = new ArrayList<>();
        RepayInfoRequest request = new RepayInfoRequest();
        request.setDealRefNo(creditFlow.getLoanNo());
        //提前结清，传02
        request.setRepayType(repayParamDTO.getRepayType());
        request.setRepayAmount(req.getAmount());
        request.setCustCutAmt(req.getCustCutAmt());
        request.setGuaRepayAmt(req.getGuaRepayAmt());
        if ("01".equals(repayParamDTO.getRepayType())) {
            List<String> term = new ArrayList<>();
            term.add(repayParamDTO.getPeriod());
            request.setTermList(term);
        }
        req.setAgreementNo(bankRepayRecord.getAgreementNo());
        repayInfoList.add(request);
        //保存还款明细
        req.setRepayInfoList(repayInfoList);
        //保存还款卡明细
        CYBKBankList cybkBank = cybkBankRepository.findByAbbrIgnoreCase(bankRepayRecord.getRepayBankCode());
        List<RepayBankRequest> repayBankList = new ArrayList<>();
        RepayBankRequest bankRequest = new RepayBankRequest();
        bankRequest.setBankCode(cybkBank.getCycfcCode());
        bankRequest.setBankName(cybkBank.getName());
        bankRequest.setPayerName(bankRepayRecord.getRepayRelUser());
        bankRequest.setMobile(bankRepayRecord.getRepayRelPhone());
        bankRequest.setPayerAcctNo(bankRepayRecord.getRepayAcctNo());
        bankRequest.setDealRefNo(creditFlow.getLoanNo());
        repayBankList.add(bankRequest);
        req.setOutPayTradeNo(bankRepayRecord.getPayOrderNo());
        req.setRepayBankList(repayBankList);
        return req;
    }

    /**
     * 只连续3期代偿 触发回购
     */
    @Override
    protected boolean isTriggerRecycle(Integer totalPeriod, Integer currentPeriod, List<BankLoanReplan> plans) {
        // 最后一期
        if (currentPeriod.compareTo(totalPeriod) == 0) {
            return false;
        }
        if (plans.size() < 2) {
            return false;
        }
        if (RepayType.CLAIM == plans.get(0).getRepayType() && RepayType.CLAIM == plans.get(1).getRepayType()) {
            return true;
        }
        // 累计6期 不回购
        return false;
    }

    @Override
    public boolean repayValidate(RepayApplyVo repayApplyVo) {
        LocalTime actTime = LocalTime.now();
        return isValidTime(actTime);
    }

    private boolean isValidTime(LocalTime actTime) {
        LocalTime start = LocalTime.parse(config.getRepayStartTime(), VALID_FORMAT);
        LocalTime end = LocalTime.parse(config.getRepayEndTime(), VALID_FORMAT);
        return actTime.isAfter(start) && actTime.isBefore(end);
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }

    @Override
    public void updatePlan(String loanId, Integer period, TrailResultVo trailResult) {
        LoanReplan repayPlan = getFinRepayService().getRepayPlan(loanId, period);
        repayPlan.setPenaltyAmt(trailResult.getOverdueFee());
        repayPlan.setBreachAmt(trailResult.getBreachFee());
        repayPlan.setTotalAmt(AmountUtil.sum(
                repayPlan.getPrincipalAmt(),
                repayPlan.getInterestAmt(),
                repayPlan.getPenaltyAmt(),
                repayPlan.getBreachAmt(),
                repayPlan.getGuaranteeAmt(),
                repayPlan.getConsultAmt()));
        getFinRepayService().updateRepayPlan(repayPlan);
    }

    @Override
    public BankRepayRecord initBankRepayRecord(TrailResultVo trailResult, String loanId, Integer period) {
        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setPrincipalAmt(AmountUtil.safeNum(trailResult.getPrincipal()));
        bankRepayRecord.setInterestAmt(AmountUtil.safeNum(trailResult.getInterest()));
        bankRepayRecord.setGuaranteeAmt(AmountUtil.safeNum(trailResult.getGuaranteeFee()));
        bankRepayRecord.setPenaltyAmt(AmountUtil.safeNum(trailResult.getOverdueFee()));
        bankRepayRecord.setBreachAmt(AmountUtil.safeNum(trailResult.getBreachFee()));
        return bankRepayRecord;
    }

    @Override
    public void populateRepayApplyVo(RepayApplyVo applyVo, String loanId) {
        // 拼装银行卡信息
        Loan loan = getCommonService().findLoanById(loanId);
        AccountBankCard bankCard = getFinRepayService().getBankCard(loan.getRepayCardId());
        String repayBankAbbr = bankCard.getBankCode();
        CYBKBankList cybkBankList = cybkBankRepository.findByAbbrIgnoreCase(repayBankAbbr);
        if (cybkBankList != null) {
            applyVo.setRepayBankName(cybkBankList.getName());
        }
        applyVo.setRepayRelUser(bankCard.getCardName());
        applyVo.setRepayAcctNo(bankCard.getCardNo());
        applyVo.setRepayBankCode(repayBankAbbr);
        applyVo.setAgreementNo(bankCard.getAgreeNo());
    }

    /**
     * 当期试算
     */
    private TrailResultVo trialCurrent(Loan loan, final Integer period, LocalDate transferDate) {
        logger.info("长银直连当期还款试算，loanId: {}", loan.getId());
        if (null == period) {
            throw new BizException(BizErrorCode.REPAY_TRIAL_ERROR.getCode(), "还款期数不能为空");
        }

        LoanReplan curPlan = getFinRepayService().getRepayPlan(loan.getId(), period);

        // 代偿后
        if (curPlan.getBankRepayStatus() == RepayStatus.REPAID) {
            BankLoanReplan bankRepayPlan = getFinRepayService().findBankRepayPlan(loan.getId(), period);
            BigDecimal actTotalAmt = Objects.nonNull(bankRepayPlan.getActTotalAmt()) ? bankRepayPlan.getActTotalAmt() : BigDecimal.ZERO;
            BigDecimal overdueFee = curPlan.getPenaltyAmt();
            //实还金额大于0, 代表代偿文件已经处理了
            if (actTotalAmt.compareTo(BigDecimal.ZERO) == 0) {
                //判断是否已经回购
                if (RepayPurpose.CLEAR == bankRepayPlan.getRepayPurpose()) {
                    List<BankRepayRecord> successRecord = bankRepayRecordRepository.findByLoanIdAndRepayStatus(loan.getId(), ProcessStatus.SUCCESS);
                    BankRepayRecord maxRecord = successRecord.stream().max(Comparator.comparing(BankRepayRecord::getPeriod)).orElseThrow();
                    //回购之后的期数,也不需要调资方试算
                    if (bankRepayPlan.getPeriod() > maxRecord.getPeriod()) {
                        return getInnerTrailResult(curPlan, overdueFee);
                    }
                }

                //调资方试算接口 获取罚息
                TrailResultVo remoteTrailResult = getRemoteTrailResult(loan, curPlan, RepayPurpose.CURRENT, transferDate);
                overdueFee = remoteTrailResult.getOverdueFee();
            }
            // 不调资方试算
            return getInnerTrailResult(curPlan, overdueFee);
        } else {

            return getRemoteTrailResult(loan, curPlan, RepayPurpose.CURRENT, transferDate);
        }
    }

    private TrailResultVo getInnerTrailResult(LoanReplan curPlan, BigDecimal overdueFee) {
        TrailResultVo resultVo = new TrailResultVo();
        resultVo.setAmount(
                sumAmount(
                        curPlan.getPrincipalAmt(),
                        curPlan.getInterestAmt(),
                        curPlan.getGuaranteeAmt(),
                        overdueFee,
                        curPlan.getBreachAmt()));
        resultVo.setPrincipal(curPlan.getPrincipalAmt());
        resultVo.setInterest(curPlan.getInterestAmt());
        resultVo.setGuaranteeFee(curPlan.getGuaranteeAmt());
        resultVo.setOverdueFee(overdueFee);
        resultVo.setBreachFee(curPlan.getBreachAmt());
        resultVo.setPayee(Payee.GUARANTEE);
        return resultVo;
    }

    private TrailResultVo getRemoteTrailResult(Loan loan, LoanReplan curPlan, RepayPurpose repayPurpose, LocalDate transferDate) {

        CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();
        // 正常结清试算
        CYBKRepayTrialRequest req = new CYBKRepayTrialRequest();
        req.setApplCde(creditFlow.getCreditNo());
        req.setLoanNo(creditFlow.getLoanNo());

        //当期、逾期还款、代偿后还款，传01
        //提前结清，传02
        String repayMode = repayPurpose == RepayPurpose.CLEAR ? "02" : "01";
        req.setRepaymentMode(repayMode);
        req.setPeriod(String.valueOf(curPlan.getPeriod()));
        //操作时间
        req.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        if (transferDate != null) {
            req.setCustSetlDt(transferDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        logger.info("长银直连结清试算请求: {}", JsonUtil.toJsonString(req));
        CYBKRepayTrialResponse resp = cybkRequestService.repayTrial(req);
        logger.info("长银直连结清试算结果: {}", JsonUtil.toJsonString(resp));

        //保存还款授权码
        creditFlow.setRepaymentCode(resp.getRepaymentCode());
        cybkCreditFlowRepository.save(creditFlow);

        //应还本金
        BigDecimal principalAmt = new BigDecimal(resp.getPsRemPrcp());
        //应还利息
        BigDecimal intAmt = new BigDecimal(resp.getIntAmt());
        //应还罚息
        BigDecimal odIntAmt = StringUtil.isBlank(resp.getOdIntAmt()) ? BigDecimal.ZERO : new BigDecimal(resp.getOdIntAmt());
        //应还利息、罚息之和
        //BigDecimal odPrcpAmt = StringUtil.isBlank(resp.getOdPrcpAmt()) ? BigDecimal.ZERO : new BigDecimal(resp.getOdPrcpAmt());
        //if (intAmt.add(odIntAmt).compareTo(odPrcpAmt) != 0) {
        //    logger.error("长银直连还款试算，资方返回的应还利息+应还罚息与应还罚息之和不相等，loanId:{},period:{}", loan.getId(), period);
        //    throw new BizException(BizErrorCode.REPAY_TRIAL_ERROR);
        //}

        TrailResultVo resultVo = new TrailResultVo();
        resultVo.setPayee(Payee.CAPITAL);
        //应还总金额 = 应还本金+应还利息+应还资方罚息+应还融担费(我司)
        resultVo.setAmount(
                sumAmount(
                        principalAmt,
                        intAmt,
                        odIntAmt,
                        curPlan.getGuaranteeAmt(),
                        curPlan.getBreachAmt()));
        resultVo.setPrincipal(principalAmt);
        resultVo.setInterest(intAmt);
        resultVo.setGuaranteeFee(safeNum(curPlan.getGuaranteeAmt()));
        resultVo.setOverdueFee(odIntAmt);
        resultVo.setBreachFee(safeNum(curPlan.getBreachAmt()));
        return resultVo;
    }


    private TrailResultVo trialClear(Loan loan, final Integer period, LocalDate transferDate) {
        logger.info("长银直连结清还款试算，loanId: {}, period: {}", loan.getId(), period);
        clearTrailCheck(loan, period);
        // 对客未还list
        List<LoanReplan> customUnpaidList = getFinRepayService().findRepayPlanByLoanIdAndCustomStatus(loan.getId(), RepayStatus.NORMAL);
        // 对客未还最小期
        LoanReplan minPlan = customUnpaidList.stream().min(Comparator.comparing(LoanReplan::getPeriod))
                .orElseThrow(() -> new BizException(BizErrorCode.CUSTOM_UNPAID_PLAN_NOT_FOUND));
        // 对资未还最小期
        LoanReplan curBankMinPlan = customUnpaidList.stream().filter(p -> p.getBankRepayStatus() == RepayStatus.NORMAL)
                .min(Comparator.comparing(LoanReplan::getPeriod)).orElse(null);

        // 传过来的期数和对客未还期数不一致
        if (Objects.nonNull(period) && !minPlan.getPeriod().equals(period)) {
            // 与对资未还期数不一致
            Integer capitalCurPeriod = Objects.nonNull(curBankMinPlan) ? curBankMinPlan.getPeriod() : 0;
            if (!period.equals(capitalCurPeriod)) {
                throw new BizException(BizErrorCode.REPAY_TRIAL_CLEAR_PERIOD_ERROR);
            }
        }

        // 计算结清累计金额
        BigDecimal existTotal = BigDecimal.ZERO;
        BigDecimal existPrincipal = BigDecimal.ZERO;
        BigDecimal existInterest = BigDecimal.ZERO;
        BigDecimal existGuarantee = BigDecimal.ZERO;
        BigDecimal existPenalty = BigDecimal.ZERO;
        BigDecimal existBreach = BigDecimal.ZERO;

        for (LoanReplan p : customUnpaidList) {
            existTotal = existTotal.add(safeNum(p.getTotalAmt()));
            existPrincipal = existPrincipal.add(safeNum(p.getPrincipalAmt()));
            //结清 利息按日计算
            //existInterest = existInterest.add(safeNum(p.getInterestAmt()));
            //结清 融担费按日计算
            //existGuarantee = existGuarantee.add(safeNum(p.getGuaranteeAmt()));
            existPenalty = existPenalty.add(safeNum(p.getPenaltyAmt()));
            existBreach = existBreach.add(safeNum(p.getBreachAmt()));
        }

        //资金占用天数
        long principalUseDays = getPrincipalUseDays(minPlan, loan);
        //按日计算融担费
        existGuarantee = getGuaranteeAmt(loan, minPlan, existPrincipal, (int) principalUseDays);

        // 对资已结清
        if (curBankMinPlan == null) {
            // 提前结清利息=资方利率/360*资金实际使用天数*剩余本金
            existInterest = getClearInterest(loan, (int) principalUseDays, existPrincipal);
            //资方罚息
            BigDecimal overdueFee = existPenalty;
            BankLoanReplan bankRepayPlan = getFinRepayService().findBankRepayPlan(loan.getId(), period);
            BigDecimal actTotalAmt = Objects.nonNull(bankRepayPlan.getActTotalAmt()) ? bankRepayPlan.getActTotalAmt() : BigDecimal.ZERO;
            //实还金额大于0 ,代表代偿文件已处理
            if (actTotalAmt.compareTo(BigDecimal.ZERO) == 0) {
                //判断回购
                if (RepayPurpose.CLEAR == bankRepayPlan.getRepayPurpose()) {
                    List<BankRepayRecord> successRecord = bankRepayRecordRepository.findByLoanIdAndRepayStatus(loan.getId(), ProcessStatus.SUCCESS);
                    BankRepayRecord maxRecord = successRecord.stream().max(Comparator.comparing(BankRepayRecord::getPeriod)).orElseThrow();
                    //回购后的期数,不需要试算
                    if (bankRepayPlan.getPeriod() > maxRecord.getPeriod()) {
                        // 不调资方试算
                        TrailResultVo resultVo = new TrailResultVo();
                        resultVo.setPayee(Payee.GUARANTEE);
                        resultVo.setAmount(
                                sumAmount(
                                        existPrincipal,
                                        existInterest,
                                        existGuarantee,
                                        overdueFee,
                                        existBreach));
                        resultVo.setPrincipal(existPrincipal);
                        resultVo.setInterest(existInterest);
                        resultVo.setGuaranteeFee(existGuarantee);
                        resultVo.setOverdueFee(overdueFee);
                        resultVo.setBreachFee(existBreach);
                        return resultVo;
                    }
                }

                //资方代偿当日,代偿文件解析前 需要调资方试算接口获取罚息
                TrailResultVo remoteTrailResult = getRemoteTrialClearResult(loan, period, existGuarantee, existBreach, transferDate);
                overdueFee = remoteTrailResult.getOverdueFee();
            }

            TrailResultVo resultVo = new TrailResultVo();
            resultVo.setPayee(Payee.GUARANTEE);
            resultVo.setAmount(
                    sumAmount(
                            existPrincipal,
                            existInterest,
                            existGuarantee,
                            overdueFee,
                            existBreach));
            resultVo.setPrincipal(existPrincipal);
            resultVo.setInterest(existInterest);
            resultVo.setGuaranteeFee(existGuarantee);
            resultVo.setOverdueFee(overdueFee);
            resultVo.setBreachFee(existBreach);
            return resultVo;
        }


        // 还款日当期不允许提前结清试算
      /*  if (curBankMinPlan.getRepayDate().isEqual(LocalDate.now())) {
            throw new BizException(BizErrorCode.REPAY_TRIAL_ERROR.getCode(), "还款日不能结清试算");
        }*/

        return getRemoteTrialClearResult(loan, period, existGuarantee, existBreach, transferDate);
    }

    private TrailResultVo getRemoteTrialClearResult(Loan loan, Integer period, BigDecimal existGuarantee, BigDecimal existBreach, LocalDate transferDate) {
        Credit credit = finCreditService.getCredit(loan.getCreditId());
        CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(credit.getId()).orElseThrow();


        CYBKRepayTrialRequest req = new CYBKRepayTrialRequest();
        req.setApplCde(credit.getCreditNo());
        req.setLoanNo(creditFlow.getLoanNo());

        //当期、逾期还款、代偿后还款，传01
        //提前结清，传02
        req.setRepaymentMode("02");
        req.setPeriod(String.valueOf(period));
        //操作时间
        req.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        if (transferDate != null) {
            req.setCustSetlDt(transferDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        logger.info("长银直连结清试算请求: {}", JsonUtil.toJsonString(req));
        CYBKRepayTrialResponse resp = cybkRequestService.repayTrial(req);
        logger.info("长银直连结清试算结果: {}", JsonUtil.toJsonString(resp));

        //保存还款授权码
        creditFlow.setRepaymentCode(resp.getRepaymentCode());
        cybkCreditFlowRepository.save(creditFlow);

        //应还本金
        BigDecimal principalAmt = StringUtil.isBlank(resp.getPsRemPrcp()) ? BigDecimal.ZERO : new BigDecimal(resp.getPsRemPrcp());
        //应还利息
        BigDecimal intAmt = StringUtil.isBlank(resp.getIntAmt()) ? BigDecimal.ZERO : new BigDecimal(resp.getIntAmt());
        //应还罚息
        BigDecimal odIntAmt = StringUtil.isBlank(resp.getOdIntAmt()) ? BigDecimal.ZERO : new BigDecimal(resp.getOdIntAmt());

        TrailResultVo resultVo = new TrailResultVo();
        resultVo.setPayee(Payee.CAPITAL);
        //应还总金额 = 应还本金+应还利息+应还资方罚息+应还融担费(我司)
        resultVo.setAmount(
                sumAmount(
                        principalAmt,
                        intAmt,
                        odIntAmt,
                        existGuarantee,
                        existBreach));
        resultVo.setPrincipal(principalAmt);
        resultVo.setInterest(intAmt);
        resultVo.setGuaranteeFee(existGuarantee);
        resultVo.setOverdueFee(odIntAmt);
        resultVo.setBreachFee(existBreach);
        return resultVo;
    }

    private long getPrincipalUseDays(LoanReplan minPlan, Loan loan) {
        //资金占用天数
        long days;
        if (minPlan.getPeriod() == 1) {
            //放款日到当前
            days = ChronoUnit.DAYS.between(loan.getLoanTime().toLocalDate(), LocalDate.now());
        } else {
            //上期还款日到今天
            LoanReplan repayPlan = getFinRepayService().getRepayPlan(loan.getId(), minPlan.getPeriod() - 1);
            days = ChronoUnit.DAYS.between(repayPlan.getRepayDate(), LocalDate.now());
        }
        logger.info("还款计划, Id:{}, 提前结清资金当月占用天数: {}", minPlan.getId(), days);
        return days;
    }

    private TrailResultVo directCurTrail(Loan loan, LoanReplan plan, LocalDate transferDate) {
        if (plan.getBankRepayStatus() == RepayStatus.REPAID) {
            //代偿后
            BankLoanReplan bankRepayPlan = getFinRepayService().findBankRepayPlan(loan.getId(), plan.getPeriod());
            BigDecimal actTotalAmt = Objects.nonNull(bankRepayPlan.getActTotalAmt()) ? bankRepayPlan.getActTotalAmt() : BigDecimal.ZERO;
            BigDecimal overdueFee = plan.getPenaltyAmt();
            //实还金额大于0, 代表代偿文件已处理
            if (actTotalAmt.compareTo(BigDecimal.ZERO) == 0) {
                if (RepayPurpose.CLEAR == bankRepayPlan.getRepayPurpose()) {
                    List<BankRepayRecord> successRecord = bankRepayRecordRepository.findByLoanIdAndRepayStatus(loan.getId(), ProcessStatus.SUCCESS);
                    BankRepayRecord maxRecord = successRecord.stream().max(Comparator.comparing(BankRepayRecord::getPeriod)).orElseThrow();
                    //回购之后的期数不需要试算
                    if (bankRepayPlan.getPeriod() > maxRecord.getPeriod()) {
                        // 不调资方试算
                        return getInnerTrailResult(plan, overdueFee);
                    }
                }

                //资方代偿当日,代偿文件解析前 需要调资方试算接口获取罚息
                TrailResultVo remoteTrailResult = getRemoteTrailResult(loan, plan, RepayPurpose.CURRENT, transferDate);
                overdueFee = remoteTrailResult.getOverdueFee();
            }
            // 不调资方试算
            return getInnerTrailResult(plan, overdueFee);
        }

        //调用资方试算
        return getRemoteTrailResult(loan, plan, RepayPurpose.CURRENT, transferDate);
    }

    private TrailResultVo directClearTrail(Loan loan, LoanReplan plan, LocalDate transferDate) {
        logger.info("请求长银直连结清试算:loanId:{},period:{}", plan.getLoanId(), plan.getPeriod());
        Integer period = plan.getPeriod();
        // 对客未还list
        List<LoanReplan> bankUnpaidList = getFinRepayService().findRepayPlanByLoanIdAndBankStatus(loan.getId(), RepayStatus.NORMAL);

        // 计算结清累计金额
        BigDecimal existTotal = BigDecimal.ZERO;
        BigDecimal existPrincipal = BigDecimal.ZERO;
        BigDecimal existInterest = BigDecimal.ZERO;
        BigDecimal existGuarantee = BigDecimal.ZERO;
        BigDecimal existPenalty = BigDecimal.ZERO;
        BigDecimal existBreach = BigDecimal.ZERO;

        for (LoanReplan p : bankUnpaidList) {
            existTotal = existTotal.add(safeNum(p.getTotalAmt()));
            existPrincipal = existPrincipal.add(safeNum(p.getPrincipalAmt()));
            //结清 利息按日计算
            //existInterest = existInterest.add(safeNum(p.getInterestAmt()));
            //结清 融担费按日计算
            //existGuarantee = existGuarantee.add(safeNum(p.getGuaranteeAmt()));
            existPenalty = existPenalty.add(safeNum(p.getPenaltyAmt()));
            existBreach = existBreach.add(safeNum(p.getBreachAmt()));
        }

        //资金占用天数
        long principalUseDays = getPrincipalUseDays(plan, loan);
        // 融担费按日计算
        existGuarantee = getGuaranteeAmt(loan, plan, existPrincipal, (int) principalUseDays);

        // 代偿后
        if (RepayStatus.REPAID == plan.getBankRepayStatus()) {
            // 提前结清利息=资方利率/360*资金实际使用天数*剩余本金
            existInterest = getClearInterest(loan, (int) principalUseDays, existPrincipal);
            //资方罚息
            BigDecimal overdueFee = existPenalty;
            BankLoanReplan bankRepayPlan = getFinRepayService().findBankRepayPlan(loan.getId(), period);
            BigDecimal actTotalAmt = Objects.nonNull(bankRepayPlan.getActTotalAmt()) ? bankRepayPlan.getActTotalAmt() : BigDecimal.ZERO;
            //实还金额大于0,代表代偿文件已处理
            if (actTotalAmt.compareTo(BigDecimal.ZERO) == 0) {
                //回购判断
                if (RepayPurpose.CLEAR == bankRepayPlan.getRepayPurpose()) {
                    List<BankRepayRecord> successRecord = bankRepayRecordRepository.findByLoanIdAndRepayStatus(loan.getId(), ProcessStatus.SUCCESS);
                    BankRepayRecord maxRecord = successRecord.stream().max(Comparator.comparing(BankRepayRecord::getPeriod)).orElseThrow();
                    //回购后的期数  不需要试算
                    if (bankRepayPlan.getPeriod() > maxRecord.getPeriod()) {
                        // 不调资方试算
                        TrailResultVo resultVo = new TrailResultVo();
                        resultVo.setPayee(Payee.GUARANTEE);
                        resultVo.setAmount(
                                sumAmount(
                                        existPrincipal,
                                        existInterest,
                                        existGuarantee,
                                        overdueFee,
                                        existBreach));
                        resultVo.setPrincipal(existPrincipal);
                        resultVo.setInterest(existInterest);
                        resultVo.setGuaranteeFee(existGuarantee);
                        resultVo.setOverdueFee(overdueFee);
                        resultVo.setBreachFee(existBreach);
                        return resultVo;
                    }
                }
                //资方代偿当日,代偿文件解析前 需要调资方试算接口获取罚息
                TrailResultVo remoteTrailResult = getRemoteTrialClearResult(loan, period, existGuarantee, existBreach, transferDate);
                overdueFee = remoteTrailResult.getOverdueFee();
            }

            TrailResultVo resultVo = new TrailResultVo();
            resultVo.setPayee(Payee.GUARANTEE);
            resultVo.setAmount(
                    sumAmount(
                            existPrincipal,
                            existInterest,
                            existGuarantee,
                            overdueFee,
                            existBreach));
            resultVo.setPrincipal(existPrincipal);
            resultVo.setInterest(existInterest);
            resultVo.setGuaranteeFee(existGuarantee);
            resultVo.setOverdueFee(overdueFee);
            resultVo.setBreachFee(existBreach);
            return resultVo;
        }

        //调资方结清试算
        return getRemoteTrialClearResult(loan, period, existGuarantee, existBreach, transferDate);
    }

    private BigDecimal getClearInterest(Loan loan, int principalUseDays, BigDecimal existPrincipal) {
        // 提前结清利息=资方利率/360*资金实际使用天数*剩余本金
        return loan.getBankRate().divide(new BigDecimal(YEAR_DAYS), PROCESSING_POINT_NUM, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(principalUseDays)).multiply(existPrincipal).setScale(TWO, RoundingMode.HALF_UP);
    }

    /**
     * 提前结清担保费= 剩余本金*资金实际使用天数*【融担费年化利率/360】
     * 融担费年化利率 = 23.95% - 资方利率(长银目前15%)
     *
     * @param loan
     * @param minPlan
     * @return
     */
    public BigDecimal getGuaranteeAmt(Loan loan, LoanReplan minPlan, BigDecimal existPrincipal, int principalDays) {

        //融担日利率 = (对客利率 - 对资利率) / 360
        BigDecimal guaranteeRate = loan.getCustomRate().subtract(loan.getBankRate()).divide(
                new BigDecimal(YEAR_DAYS), PROCESSING_POINT_NUM, RoundingMode.HALF_UP);

        // 提前结清融担费 = 剩余本金 * 资金实际占用天数 * 融担费日利率
        BigDecimal guaranteeFee = existPrincipal.multiply(new BigDecimal(principalDays)).multiply(guaranteeRate).setScale(2, RoundingMode.HALF_UP);
        logger.info("借据, loanId: {}, 开始期数: {}, 提前结清担保费: {}", loan.getId(), minPlan.getPeriod(), guaranteeFee);
        return guaranteeFee;
    }

    /**
     * 正常还款
     *
     * @return 还款结果
     */
    public RepayResultVo repayNormal(Loan loan, BankRepayRecord bankRepayRecord, LoanReplan originPlan,
                                     LocalDate transferDate, BigDecimal reduceAmt) {
        if (bankRepayRecord == null) {
            throw new BizException(BizErrorCode.BANK_REPAY_NOT_FOUND);
        }

        CYBKRepayParamDTO repayParamDTO = new CYBKRepayParamDTO();
        repayParamDTO.setRepayRecordId(bankRepayRecord.getId());
        repayParamDTO.setTotalPrincipal(bankRepayRecord.getPrincipalAmt());
        repayParamDTO.setTotalInterest(bankRepayRecord.getInterestAmt());
        repayParamDTO.setTotalPenalty(bankRepayRecord.getPenaltyAmt()); //对资 应还罚息
        repayParamDTO.setGuaranteeAmt(bankRepayRecord.getGuaranteeAmt());
        repayParamDTO.setPeriod(String.valueOf(bankRepayRecord.getPeriod()));
        repayParamDTO.setBreachAmt(bankRepayRecord.getBreachAmt()); //对资 应还违约金

        if (Objects.equals(bankRepayRecord.getRepayMode(), RepayMode.OFFLINE)) {
            // 客户线下还款 传Y
            repayParamDTO.setRepayMode("Y");
            repayParamDTO.setReduceGuaranteeAmt(
                    repayParamDTO.getGuaranteeAmt().compareTo(reduceAmt) > 0 ? reduceAmt : repayParamDTO.getGuaranteeAmt());
        } else {
            // 客户线上还款 传N
            repayParamDTO.setRepayMode("N");
            repayParamDTO.setReduceGuaranteeAmt(BigDecimal.ZERO);
        }

        //当期、逾期还款、代偿后还款，传01
        //提前结清，传02
        repayParamDTO.setRepayType("01");
        repayParamDTO.setTransferDate(transferDate);

        RepayResultVo resultVo = new RepayResultVo();

        CYBKRepayApplyRequest request = buildRepayParam(loan, repayParamDTO, bankRepayRecord);
        logger.info("长银直连当期还款请求参数={}", JsonUtil.toJsonString(request));
        CYBKRepayApplyResponse resp = null;
        try {
            resp = cybkRequestService.repayApply(request);
            logger.info("长银直连当期还款返回结果={}", JsonUtil.toJsonString(resp));
        } catch (BizException ex) {
            logger.error("长银直连当期还款返回结果异常", ex);
        }

        if (resp != null) {
            resultVo.setBankSeq(resp.getSetlSeq());
        }
        // 还款申请 一律以查询接口为最终结果
        resultVo.setStatus(ProcessStatus.PROCESSING);

        return resultVo;
    }


    /**
     * 提前结清
     *
     * @param loan            借据
     * @param bankRepayRecord 对资还款记录
     * @return 还款结果
     */
    public RepayResultVo repayClear(Loan loan, BankRepayRecord bankRepayRecord, LoanReplan originPlan, LocalDate transferDate, BigDecimal reduceAmt) {
        if (bankRepayRecord == null) {
            throw new BizException(BizErrorCode.BANK_REPAY_NOT_FOUND);
        }

        CYBKRepayParamDTO repayParamDTO = new CYBKRepayParamDTO();
        repayParamDTO.setRepayRecordId(bankRepayRecord.getId());
        repayParamDTO.setTotalPrincipal(bankRepayRecord.getPrincipalAmt());
        repayParamDTO.setTotalInterest(bankRepayRecord.getInterestAmt());
        repayParamDTO.setTotalPenalty(bankRepayRecord.getPenaltyAmt()); //对资 应还罚息
        repayParamDTO.setGuaranteeAmt(bankRepayRecord.getGuaranteeAmt());
        repayParamDTO.setPeriod(String.valueOf(bankRepayRecord.getPeriod()));
        repayParamDTO.setBreachAmt(bankRepayRecord.getBreachAmt()); //对资 应还违约金

        if (Objects.equals(bankRepayRecord.getRepayMode(), RepayMode.OFFLINE)) {
            // 客户线下还款 传Y
            repayParamDTO.setRepayMode("Y");
            repayParamDTO.setReduceGuaranteeAmt(
                    repayParamDTO.getGuaranteeAmt().compareTo(reduceAmt) > 0 ? reduceAmt : repayParamDTO.getGuaranteeAmt());
        } else {
            // 客户线上还款 传N
            repayParamDTO.setRepayMode("N");
            repayParamDTO.setReduceGuaranteeAmt(BigDecimal.ZERO);
        }

        //当期、逾期还款、代偿后还款，传01
        //提前结清，传02
        repayParamDTO.setRepayType("02");
        repayParamDTO.setTransferDate(transferDate);

        RepayResultVo resultVo = new RepayResultVo();

        CYBKRepayApplyRequest request = buildRepayParam(loan, repayParamDTO, bankRepayRecord);
        logger.info("长银直连结清还款请求参数={}", JsonUtil.toJsonString(request));
        CYBKRepayApplyResponse resp = null;
        try {
            resp = cybkRequestService.repayApply(request);
            logger.info("长银直连结清还款申请 返回结果={}", JsonUtil.toJsonString(resp));
        } catch (BizException ex) {
            logger.error("长银直连结清还款申请 返回结果异常", ex);
        }

        if (resp != null) {
            resultVo.setBankSeq(resp.getSetlSeq());
        }
        // 还款申请 一律以查询结果为最终结果
        resultVo.setStatus(ProcessStatus.PROCESSING);

        return resultVo;
    }

}
