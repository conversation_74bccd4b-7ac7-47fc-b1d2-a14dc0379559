package com.jinghang.capital.core.util;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 计算逾期时间
 */
public class OverdueUtil {


    /**
     * 计算到2个日期之间的天数
     *
     * @param preDay    小的日期
     * @param expectedDay 指定日期
     * @return 天数
     */
    public static long calcDayDiff(LocalDate preDay, LocalDate expectedDay) {
        return Math.abs(ChronoUnit.DAYS.between(preDay, expectedDay));
    }

}
