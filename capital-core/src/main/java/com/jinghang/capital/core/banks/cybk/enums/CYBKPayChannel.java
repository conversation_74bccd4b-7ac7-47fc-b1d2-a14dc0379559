package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.api.dto.ProtocolChannel;

/**
 * 支付渠道
 */
public enum CYBKPayChannel {
    YEEPAY("yeepay", "易宝"),
    BAOFU("baofu", "宝付"),
    LIANLIAN("lianlian", "连连"),
    ALLINPAY("allinpay", "通联");


    /**
     * 码值
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;


    public static CYBKPayChannel findByProtocolChannel(ProtocolChannel protocolChannel) {
        return switch (protocolChannel) {
            case BF -> BAOFU;
            case ALLIN_PAY -> ALLINPAY;
            case YEE_PAY -> YEEPAY;
        };

    }

    CYBKPayChannel(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
