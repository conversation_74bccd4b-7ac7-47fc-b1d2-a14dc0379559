package com.jinghang.capital.core.banks.cybk.dto.repay;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRepayPlanQueryRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.REPAY_PLAN_QUERY;


    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银放款流水号
     */
    private String loanSeq;
    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 查询类型
     */
    private String enqTyp;

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getEnqTyp() {
        return enqTyp;
    }

    public void setEnqTyp(String enqTyp) {
        this.enqTyp = enqTyp;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }
}
