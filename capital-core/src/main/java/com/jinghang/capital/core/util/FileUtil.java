package com.jinghang.capital.core.util;


import com.jinghang.common.sftp.DestMapping;
import com.jinghang.common.sftp.Sftp;
import com.jinghang.common.sftp.exception.SftpException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件处理工具类
 *
 * @@@ 公有方法里只能有一行代码 方便查找
 */
public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 创建临时文件 File
     *
     * @return
     */
    public static File createTempFile() {
        return createTempFileFulfil("temp", "temp");
    }

    /**
     * 创建临时文件 File
     *
     * @return
     */
    public static File createTempFile(String prefix, String suffix) {
        return createTempFileFulfil(prefix, suffix);
    }

    /**
     * 创建临时文件 Path
     *
     * @return
     */
    public static Path createTempPath() {
        return createTempPathFulfil("temp", "temp");
    }

    /**
     * 创建临时文件 Path
     *
     * @return
     */
    public static Path createTempPath(String prefix, String suffix) {
        return createTempPathFulfil(prefix, suffix);
    }

    /**
     * InputStream 转 Path
     *
     * @return
     */
    public static Path toPath(InputStream inputStream) {
        return toPathFulfil(inputStream);
    }

    /**
     * base64String 转 Path
     *
     * @return
     */
    public static Path base64ToPath(String base64String) {
        return base64ToPathFulfil(base64String);
    }


    /**
     * 删除临时文件 Path
     *
     * @param path
     */

    public static void deletePathFile(Path path) {
        if (path != null && Files.exists(path)) {
            try {
                Files.delete(path);
            } catch (IOException e) {
                logger.error("delete temp Path error", e);
            }
        }
    }
    /**
     * 删除临时文件 Path
     *
     * @param file 文件
     */
    public static void deleteFile(File file) {
        if (file != null && file.exists()) {
            try {
                file.delete();
            } catch (Exception e) {
                logger.error("delete temp error", e);
            }
        }
    }

    /**
     * 密钥登录sftp，注册密钥
     *
     * @param resourcePath resource下的文件路径
     * @return
     */
    public static void sftpRegistrationKey(String resourcePath) {
        sftpRegistrationKeyFulfil(resourcePath);
    }

    /**
     * 将多个文件压缩为tar
     *
     * @param tarEntryMap Map<压缩条目名称，文件byte[]>
     * @return 压缩完成的临时tar文件
     */
    public static File tarFiles(Map<String, byte[]> tarEntryMap) {
        return tarFilesFulfil(tarEntryMap);
    }

    /**
     * 将多个文件压缩为ZIP
     *
     * @param zipEntryMap Map<压缩条目名称，文件byte[]>
     * @return 压缩完成的临时ZIP文件
     */
    public static File zipFiles(Map<String, byte[]> zipEntryMap) {
        return zipFilesFulfil(zipEntryMap);
    }

    /**
     * 上传文件到sftp bate[]数据文件
     *
     * @param sftp     需要上传的目标sftp
     * @param data     需要上传的数据
     * @param sftpPath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    public static void uploadSftp(Sftp sftp, byte[] data, String sftpPath) {
        uploadSftpFulfil(sftp, data, sftpPath);
    }

    /**
     * 上传文件到sftp File文件
     *
     * @param sftp     需要上传的目标sftp
     * @param tempFile 需要写入的临时文件
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    public static void uploadSftp(Sftp sftp, File tempFile, String filePath) {
        uploadSftpFulfil(sftp, tempFile, filePath);
    }

    /**
     * 上传文件到sftp File文件
     *
     * @param sftp     需要上传的目标sftp
     * @param tempPath 需要写入的临时文件
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    public static void uploadSftp(Sftp sftp, Path tempPath, String filePath) {
        uploadSftpFulfil(sftp, tempPath, filePath);
    }

    /**
     * 上传文件到sftp String数据文件
     *
     * @param sftp     需要上传的目标sftp
     * @param data     需要写入的数据
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    public static void uploadSftp(Sftp sftp, String data, String filePath) {
        uploadSftpFulfil(sftp, data, filePath);
    }

    /**
     * Aes加密后文件上传文件到sftp String数据文件
     *
     * @param sftp         需要上传的目标sftp
     * @param data         需要写入的数据
     * @param filePath     上传文件路径（完整路径，包括文件名及文件类型后缀）
     * @param aesBase64Key 加密文件密钥
     */
    public static void uploadSftp(Sftp sftp, String data, String filePath, String aesBase64Key) {
        uploadSftpFulfil(sftp, data, filePath, aesBase64Key);
    }

    /**
     * 批量上传文件到sftp，byte[]类型文件数据
     *
     * @param sftp          需要上传的目标sftp
     * @param uploadSftpMap 需要上传的多个数据 Map< sftp完整路径(包含文件名), String类型数据 >
     */
    public static void bathUploadSftpByBytes(Sftp sftp, Map<String, byte[]> uploadSftpMap) {
        bathUploadSftpByBytesFulfil(sftp, uploadSftpMap);
    }

    /**
     * 批量上传文件到sftp，String类型文件数据
     *
     * @param sftp          需要上传的目标sftp
     * @param uploadSftpMap 需要上传的多个数据 Map< sftp完整路径(包含文件名), String类型数据 >
     */
    public static void bathUploadSftpByStr(Sftp sftp, Map<String, String> uploadSftpMap) {
        bathUploadSftpByStrFulfil(sftp, uploadSftpMap);
    }

    /**
     * 数据写入到本地临时文件 Path String类型数据
     *
     * @param data
     * @return
     */
    public static Path writeDataToPath(String data) {
        return writeDataToPathFulfil(data);
    }

    /**
     * 获取数据MD5签名 String类型数据
     *
     * @param data
     * @return
     */
    public static String md5(String data) {
        return md5Fulfil(data);
    }

    /**
     * 从sftp下载文件到本地
     *
     * @param sftp     需要上传的目标sftp
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    public static Path downloadSftpToPath(Sftp sftp, String filePath) {
        return downloadSftpToPathFulfil(sftp, filePath);
    }

    /**
     * 批量从sftp下载文件到本地
     *
     * @param sftp      需要上传的目标sftp
     * @param filePaths 批量上传文件路径（完整路径，包括文件名及文件类型后缀）
     * @return Map<sftp路径, path>
     */
    public static Map<String, Path> batchDownloadSftp(Sftp sftp, List<String> filePaths) {
        return batchDownloadSftpFulfil(sftp, filePaths);
    }

    ///@@@@@@@@ 公共方法结束----------------------------------------------------------


    /**
     * 从sftp下载文件到本地（私有方法）
     *
     * @param sftp     需要上传的目标sftp
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    private static Path downloadSftpToPathFulfil(Sftp sftp, String filePath) {
        logger.info("从sftp下载协议文件，sftp路径：{}", filePath);
        Path tempPath = createTempPathFulfil();
        try {
            sftp.download(false, DestMapping.of(filePath, tempPath.toAbsolutePath().toString()));
            return tempPath;
        } catch (SftpException e) {
            logger.error("文件下载异常", e);
            deletePathFile(tempPath);
            return null;
        }
    }

    /**
     * 批量从sftp下载文件到本地（私有方法）
     *
     * @param sftp      需要上传的目标sftp
     * @param filePaths 批量上传文件路径（完整路径，包括文件名及文件类型后缀）
     * @return Map<sftp路径, path>
     */
    private static Map<String, Path> batchDownloadSftpFulfil(Sftp sftp, List<String> filePaths) {
        // 创建临时文件
        List<BatchSftpPath> batchSftpPaths = new ArrayList<>();
        for (String filePath : filePaths) {
            BatchSftpPath batchSftpPath = new BatchSftpPath();
            batchSftpPath.setSftpPath(filePath);
            batchSftpPath.setTempPath(createTempPathFulfil());
            batchSftpPaths.add(batchSftpPath);
        }
        // 从sftp下载文件
        batchDownloadSftpToPath(sftp, batchSftpPaths);
        return batchSftpPaths.stream().collect(Collectors.toMap(BatchSftpPath::getSftpPath, BatchSftpPath::getTempPath));
    }

    /**
     * 批量从sftp下载文件到本地（私有方法）
     *
     * @param sftp              需要上传的目标sftp
     * @param batchSftpPathList 需上传的文件信息list
     */
    private static void batchDownloadSftpToPath(Sftp sftp, List<BatchSftpPath> batchSftpPathList) {
        try {
            DestMapping[] destMappingArray = new DestMapping[batchSftpPathList.size()];
            for (int i = 0; i < batchSftpPathList.size(); i++) {
                BatchSftpPath batchSftpPath = batchSftpPathList.get(i);
                destMappingArray[i] = DestMapping.of(batchSftpPath.getSftpPath(), batchSftpPath.getTempPath().toAbsolutePath().toString());
            }
            sftp.download(destMappingArray);
        } catch (SftpException e) {
            logger.error("文件上传异常", e);
            throw new RuntimeException("文件上传异常", e);
        }
    }

    /**
     * 获取数据MD5签名 String类型数据（私有方法）
     *
     * @param data
     * @return
     */
    private static String md5Fulfil(String data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.reset();
            md.update(data.trim().getBytes(StandardCharsets.UTF_8));
            byte[] result = md.digest();
            return Hex.encodeHexString(result);
        } catch (NoSuchAlgorithmException e) {
            // no throw
        }
        return "";
    }

    /**
     * 数据写入到本地临时文件 Path String类型数据
     *
     * @param data
     * @return
     */
    private static Path writeDataToPathFulfil(String data) {

        // 创建临时文件
        Path tempPath = createTempPathFulfil();

        // 数据写入临时文件
        writeData(tempPath, data);

        return tempPath;
    }

    /**
     * 批量上传文件到sftp，byte[]类型文件数据（私有方法）
     *
     * @param sftp          需要上传的目标sftp
     * @param uploadSftpMap 需要上传的多个数据 Map< sftp完整路径(包含文件名), String类型数据 >
     */
    private static void bathUploadSftpByBytesFulfil(Sftp sftp, Map<String, byte[]> uploadSftpMap) {

        // map转list 并创建临时文件
        List<BatchSftpPath> batchSftpPaths = new ArrayList<>();
        for (String sftpPath : uploadSftpMap.keySet()) {
            BatchSftpPath batchSftpPath = new BatchSftpPath();
            batchSftpPath.setSftpPath(sftpPath);
            batchSftpPath.setByteData(uploadSftpMap.get(sftpPath));
            batchSftpPath.setTempPath(createTempPathFulfil());
            batchSftpPaths.add(batchSftpPath);
        }

        // 数据写入临时文件
        batchWriteDataByBytes(batchSftpPaths);

        // 上传文件到sftp
        uploadFiles(sftp, batchSftpPaths);

        // 删除临时文件
        deletePathList(batchSftpPaths);
    }

    /**
     * 批量上传文件到sftp，String类型文件数据（私有方法）
     *
     * @param sftp          需要上传的目标sftp
     * @param uploadSftpMap 需要上传的多个数据 Map< sftp完整路径(包含文件名), String类型数据 >
     */
    private static void bathUploadSftpByStrFulfil(Sftp sftp, Map<String, String> uploadSftpMap) {

        // map转list 并创建临时文件
        List<BatchSftpPath> batchSftpPaths = new ArrayList<>();
        for (String sftpPath : uploadSftpMap.keySet()) {
            BatchSftpPath batchSftpPath = new BatchSftpPath();
            batchSftpPath.setSftpPath(sftpPath);
            batchSftpPath.setStrData(uploadSftpMap.get(sftpPath));
            batchSftpPath.setTempPath(createTempPathFulfil());
            batchSftpPaths.add(batchSftpPath);
        }

        // 数据写入临时文件
        batchWriteDataByStr(batchSftpPaths);

        // 上传文件到sftp
        uploadFiles(sftp, batchSftpPaths);

        // 删除临时文件
        deletePathList(batchSftpPaths);
    }

    /**
     * 上传文件到sftp bate[]数据文件（私有方法）
     *
     * @param sftp     需要上传的目标sftp
     * @param data     需要上传的数据
     * @param sftpPath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    private static void uploadSftpFulfil(Sftp sftp, byte[] data, String sftpPath) {

        // 创建临时文件
        List<BatchSftpPath> batchSftpPaths = new ArrayList<>();
        BatchSftpPath batchSftpPath = new BatchSftpPath();
        batchSftpPath.setByteData(data);
        batchSftpPath.setSftpPath(sftpPath);
        batchSftpPath.setTempPath(createTempPathFulfil());
        batchSftpPaths.add(batchSftpPath);

        // 数据写入临时文件
        batchWriteDataByBytes(batchSftpPaths);

        // 上传文件到sftp
        uploadFiles(sftp, batchSftpPaths);

        // 删除临时文件
        deletePathList(batchSftpPaths);
    }

    /**
     * 上传文件到sftp（私有方法）
     *
     * @param sftp     需要上传的目标sftp
     * @param tempFile 需要写入的临时文件
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    private static void uploadSftpFulfil(Sftp sftp, File tempFile, String filePath) {

        // 上传文件到sftp
        uploadFiles(sftp, filePath, tempFile);

        // 删除临时文件
        deleteFile(tempFile);
    }

    /**
     * 上传文件到sftp（私有方法）
     *
     * @param sftp     需要上传的目标sftp
     * @param tempPath 需要写入的临时文件
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    private static void uploadSftpFulfil(Sftp sftp, Path tempPath, String filePath) {

        // 上传文件到sftp
        uploadFiles(sftp, filePath, tempPath.toFile());

        // 删除临时文件
        deletePathFile(tempPath);
    }

    /**
     * 上传文件到sftp（私有方法）
     *
     * @param sftp     需要上传的目标sftp
     * @param data     需要写入的数据
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    private static void uploadSftpFulfil(Sftp sftp, String data, String filePath) {
        // 创建临时文件
        File tempFile = createTempFileFulfil();

        // 数据写入临时文件
        writeData(tempFile, data);

        // 上传文件到sftp
        uploadFiles(sftp, filePath, tempFile);

        // 删除临时文件
        deleteFile(tempFile);
    }

    /**
     * Aes加密后文件上传文件到sftp（私有方法）
     *
     * @param sftp         需要上传的目标sftp
     * @param data         需要写入的数据
     * @param filePath     上传文件路径（完整路径，包括文件名及文件类型后缀）
     * @param aesBase64Key 加密文件密钥
     */
    private static void uploadSftpFulfil(Sftp sftp, String data, String filePath, String aesBase64Key) {

        // 创建临时文件
        File tempFile = createTempFileFulfil();

        // 数据写入临时文件
        writeData(tempFile, data, aesBase64Key);

        // 上传文件到sftp
        uploadFiles(sftp, filePath, tempFile);

        // 删除临时文件
        deleteFile(tempFile);
    }

    /**
     * 将多个文件压缩为tar（私有方法）
     *
     * @param tarEntryMap Map<压缩条目名称，文件byte[]>
     * @return 压缩完成的临时tar文件
     */
    private static File tarFilesFulfil(Map<String, byte[]> tarEntryMap) {
        File tempFile = createTempFileFulfil("tar", "tar");
        try (TarArchiveOutputStream tarOs = new TarArchiveOutputStream(new FileOutputStream(tempFile))) {
            for (String tarEntryName : tarEntryMap.keySet()) {
                // 创建压缩条目
                TarArchiveEntry entry = new TarArchiveEntry(tarEntryName);
                // 设置条目大小
                entry.setSize(tarEntryMap.get(tarEntryName).length);
                // 放入存档条目
                tarOs.putArchiveEntry(entry);
                // 写入数据
                tarOs.write(tarEntryMap.get(tarEntryName));
                // 关闭条目
                tarOs.closeArchiveEntry();
            }
        } catch (IOException e) {
            logger.error("文件压缩为tar异常", e);
            throw new RuntimeException("文件压缩为tar异常", e);
        }
        return tempFile;
    }

    /**
     * 将多个文件压缩为ZIP（私有方法）
     *
     * @param zipEntryMap Map<压缩条目名称，文件byte[]>
     * @return 压缩完成的临时ZIP文件
     */
    private static File zipFilesFulfil(Map<String, byte[]> zipEntryMap) {
        // 创建临时ZIP文件
        File tempFile = createTempFileFulfil("zip", ".zip");
        try (ZipOutputStream zipOs = new ZipOutputStream(new FileOutputStream(tempFile))) {
            for (Map.Entry<String, byte[]> zipEntry : zipEntryMap.entrySet()) {
                // 创建压缩条目
                ZipEntry entry = new ZipEntry(zipEntry.getKey());
                // 放入ZIP条目
                zipOs.putNextEntry(entry);
                // 写入数据
                zipOs.write(zipEntry.getValue());
                // 关闭条目
                zipOs.closeEntry();
            }
        } catch (IOException e) {
            logger.error("文件压缩为ZIP异常", e);
            throw new RuntimeException("文件压缩为ZIP异常", e);
        }
        return tempFile;
    }

    /**
     * 密钥登录sftp，注册密钥（私有方法）
     *
     * @param resourcePath resource下的文件路径
     * @return
     */
    private static void sftpRegistrationKeyFulfil(String resourcePath) {
        // 创建临时文件
        File tempFile = createTempFileFulfil();

        // 将resource下的文件转存到临时文件
        try (InputStream keyInStream = FileUtil.class.getClassLoader().getResourceAsStream(resourcePath);
             FileOutputStream fileOutStream = new FileOutputStream(tempFile)) {
            if (keyInStream != null) {
                fileOutStream.write(keyInStream.readAllBytes());
            }
        } catch (Exception e) {
            logger.error("resource下的文件转存到临时文件异常", e);
            throw new RuntimeException(e);
        }

        // sftp注册密钥
        try {
            Sftp.addIdentity(tempFile.getAbsolutePath());
        } catch (SftpException e) {
            logger.error("sftp 密钥注册失败");
            throw new RuntimeException("sftp 密钥注册失败", e);
        }

        // 删除临时文件
        deleteFile(tempFile);
    }



    /**
     * 创建临时文件 Path（私有方法）
     *
     * @return
     */
    private static Path createTempPathFulfil(String prefix, String suffix) {
        try {
            return Files.createTempFile(prefix, suffix);
        } catch (IOException e) {
            logger.error("创建临时文件异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建临时文件 Path（私有方法）
     *
     * @return
     */
    private static Path createTempPathFulfil() {
        try {
            return Files.createTempFile("temp", "temp");
        } catch (IOException e) {
            logger.error("创建临时文件异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建临时文件 File（私有方法）
     *
     * @return
     */
    private static File createTempFileFulfil(String prefix, String suffix) {
        try {
            return File.createTempFile(prefix, suffix);
        } catch (IOException e) {
            logger.error("创建临时文件异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建临时文件 File（私有方法）
     *
     * @return
     */
    private static File createTempFileFulfil() {
        try {
            return File.createTempFile("temp", "temp");
        } catch (IOException e) {
            logger.error("创建临时文件异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * InputStream 转 Path（私有方法）
     *
     * @return
     */
    private static Path toPathFulfil(InputStream inputStream) {
        if (inputStream == null) {
            return null;
        }
        try {
            // 将文件流写入本地临时文件
            Path tempPath = createTempPathFulfil();
            Files.write(tempPath, inputStream.readAllBytes());
            inputStream.close();
            return tempPath;
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * base64String 转 Path（私有方法）
     *
     * @return
     */
    private static Path base64ToPathFulfil(String base64String) {
        if (base64String == null) {
            return null;
        }
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(Base64.decodeBase64(base64String))) {
            return toPathFulfil(inputStream);
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }


    /**
     * 将数据写入到临时文件（私有方法）
     *
     * @param tempFile 本地生成的临时文件
     * @param data     需要写入的数据
     */
    private static void writeData(File tempFile, byte[] data) {
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 将文件流写入本地临时文件
            fos.write(data);
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 将数据写入到临时文件（私有方法）
     *
     * @param tempPath 本地生成的临时文件
     * @param data     需要写入的数据
     */
    private static void writeData(Path tempPath, byte[] data) {
        try {
            // 将文件流写入本地临时文件
            Files.write(tempPath, data);
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 将数据写入到临时文件（私有方法）
     *
     * @param tempFile 本地生成的临时文件
     * @param data     需要写入的数据
     */
    private static void writeData(File tempFile, String data) {
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 将文件流写入本地临时文件
            fos.write(data.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 将数据写入到临时文件（私有方法）
     *
     * @param tempPath 本地生成的临时文件
     * @param data     需要写入的数据
     */
    private static void writeData(Path tempPath, String data) {
        try {
            // 将文件流写入本地临时文件
            Files.writeString(tempPath, data);
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 将数据加密后，写入到临时文件（私有方法）
     *
     * @param tempFile 本地生成的临时文件
     * @param data     需要写入的数据
     */
    private static void writeData(File tempFile, String data, String aesBase64Key) {
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 将文件流写入本地临时文件
            fos.write(AESUtil.encrypt(data.getBytes(StandardCharsets.UTF_8), aesBase64Key));
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 批量写入数据到临时文件 byte[]类型数据（私有方法）
     *
     * @param batchSftpPaths list
     */
    private static void batchWriteDataByBytes(List<BatchSftpPath> batchSftpPaths) {
        for (BatchSftpPath batchSftpPath : batchSftpPaths) {
            writeData(batchSftpPath.getTempPath(), batchSftpPath.getByteData());
        }
    }

    /**
     * 批量写入数据到临时文件 String类型数据（私有方法）
     *
     * @param batchSftpPaths list
     */
    private static void batchWriteDataByStr(List<BatchSftpPath> batchSftpPaths) {
        for (BatchSftpPath batchSftpPath : batchSftpPaths) {
            writeData(batchSftpPath.getTempPath(), batchSftpPath.getStrData());
        }
    }

    /**
     * 上传文件到sftp（私有方法）
     *
     * @param sftp     需要上传的目标sftp
     * @param filePath 上传文件路径
     * @param filePath 本地生成的临时文件
     */
    private static void uploadFiles(Sftp sftp, String filePath, File tempFile) {
        logger.info("上传文件到sftp, filePath: {}", filePath);
        try {
            sftp.upload(DestMapping.of(filePath, tempFile.getAbsolutePath()));
        } catch (SftpException e) {
            logger.error("文件上传异常", e);
            throw new RuntimeException("文件上传异常", e);
        }
    }

    /**
     * 上传文件到sftp（私有方法）
     *
     * @param sftp           需要上传的目标sftp
     * @param batchSftpPaths 需上传的文件信息list
     */
    private static void uploadFiles(Sftp sftp, List<BatchSftpPath> batchSftpPaths) {
        try {
            DestMapping[] destMappingArray = new DestMapping[batchSftpPaths.size()];
            for (int i = 0; i < batchSftpPaths.size(); i++) {
                BatchSftpPath batchSftpPath = batchSftpPaths.get(i);
                destMappingArray[i] = DestMapping.of(batchSftpPath.getSftpPath(), batchSftpPath.getTempPath().toAbsolutePath().toString());
            }
            sftp.upload(destMappingArray);
        } catch (SftpException e) {
            logger.error("文件上传异常", e);
            throw new RuntimeException("文件上传异常", e);
        }
    }

    /**
     * 删除临时Path（私有方法）
     *
     * @param batchSftpPaths
     */
    private static void deletePathList(List<BatchSftpPath> batchSftpPaths) {
        if (batchSftpPaths != null && !batchSftpPaths.isEmpty()) {
            for (BatchSftpPath batchSftpPath : batchSftpPaths) {
                deletePathFile(batchSftpPath.getTempPath());
            }
        }
    }

    /**
     * 批量文件处理实体类 （内部类，只允许当前类内部使用）
     */
    private static class BatchSftpPath {
        /**
         * 需要写入的String类型数据
         */
        private String strData;
        /**
         * 需要写入的byte[]类型数据
         */
        private byte[] byteData;
        /**
         * sftp路径(包含文件名)
         */
        private String sftpPath;
        /**
         * 已写入数据的本地临时文件
         */
        private Path tempPath;

        public String getStrData() {
            return strData;
        }

        public void setStrData(String strData) {
            this.strData = strData;
        }

        public byte[] getByteData() {
            return byteData;
        }

        public void setByteData(byte[] byteData) {
            this.byteData = byteData;
        }

        public String getSftpPath() {
            return sftpPath;
        }

        public void setSftpPath(String sftpPath) {
            this.sftpPath = sftpPath;
        }

        public Path getTempPath() {
            return tempPath;
        }

        public void setTempPath(Path tempPath) {
            this.tempPath = tempPath;
        }
    }

}
