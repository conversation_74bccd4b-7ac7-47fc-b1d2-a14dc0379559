package com.jinghang.capital.core.banks.cybk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCreditApplyResponse {

    /**
     * 外部授信流水号
     */
    private String outApplSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银客户号
     */
    private String custId;
    /**
     * 担保公司码值
     */
    private String guarCompanyCode;
    /**
     * 担保公司名称
     */
    private String guarCompanyName;

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getGuarCompanyCode() {
        return guarCompanyCode;
    }

    public void setGuarCompanyCode(String guarCompanyCode) {
        this.guarCompanyCode = guarCompanyCode;
    }

    public String getGuarCompanyName() {
        return guarCompanyName;
    }

    public void setGuarCompanyName(String guarCompanyName) {
        this.guarCompanyName = guarCompanyName;
    }
}
