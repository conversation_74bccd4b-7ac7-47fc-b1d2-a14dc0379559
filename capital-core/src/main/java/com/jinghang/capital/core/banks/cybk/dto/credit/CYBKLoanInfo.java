package com.jinghang.capital.core.banks.cybk.dto.credit;


public class CYBKLoanInfo {


    /**
     * 外部授信流水号
     */
    private String outApplSeq;
    /**
     *商户码值
     */
    private String merchantNo;
    /**
     *门店码值
     */
    private String storeCode;
    /**
     *终端类型
     */
    private String terminalType;
    /**
     *申请日期
     */
    private String applyDt;
    /**
     *长银活体认证流水号
     */
    private String faceCde;
    /**
     *人脸识别分数
     */
    private String faceScore;
    /**
     *人脸识别渠道
     */
    private String faceSource;
    /**
     *贷款类型
     */
    private String typGrp;
    /**
     *贷款系列
     */
    private String typSerial;
    /**
     *贷款品种
     */
    private String loanTyp;
    /**
     *申请金额
     */
    private String applyAmt;
    /**
     *申请期限
     */
    private String applyTnr;
    /**
     *定价利率
     */
    private String priceIntRat;
    /**
     *对客展示利率
     */
    private String custDayRate;
    /**
     *还款方式
     */
    private String mtdCde;
    /**
     *还款间隔
     */
    private String loanFreq;
    /**
     *每期还款日
     */
    private String dueDayOpt;
    /**
     *还款日
     */
    private String dueDay;
    /**
     *回调地址
     */
    private String callbackUrl;

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType;
    }

    public String getApplyDt() {
        return applyDt;
    }

    public void setApplyDt(String applyDt) {
        this.applyDt = applyDt;
    }

    public String getFaceCde() {
        return faceCde;
    }

    public void setFaceCde(String faceCde) {
        this.faceCde = faceCde;
    }

    public String getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(String faceScore) {
        this.faceScore = faceScore;
    }

    public String getFaceSource() {
        return faceSource;
    }

    public void setFaceSource(String faceSource) {
        this.faceSource = faceSource;
    }

    public String getTypGrp() {
        return typGrp;
    }

    public void setTypGrp(String typGrp) {
        this.typGrp = typGrp;
    }

    public String getTypSerial() {
        return typSerial;
    }

    public void setTypSerial(String typSerial) {
        this.typSerial = typSerial;
    }

    public String getLoanTyp() {
        return loanTyp;
    }

    public void setLoanTyp(String loanTyp) {
        this.loanTyp = loanTyp;
    }

    public String getApplyAmt() {
        return applyAmt;
    }

    public void setApplyAmt(String applyAmt) {
        this.applyAmt = applyAmt;
    }

    public String getApplyTnr() {
        return applyTnr;
    }

    public void setApplyTnr(String applyTnr) {
        this.applyTnr = applyTnr;
    }

    public String getPriceIntRat() {
        return priceIntRat;
    }

    public void setPriceIntRat(String priceIntRat) {
        this.priceIntRat = priceIntRat;
    }

    public String getCustDayRate() {
        return custDayRate;
    }

    public void setCustDayRate(String custDayRate) {
        this.custDayRate = custDayRate;
    }

    public String getMtdCde() {
        return mtdCde;
    }

    public void setMtdCde(String mtdCde) {
        this.mtdCde = mtdCde;
    }

    public String getLoanFreq() {
        return loanFreq;
    }

    public void setLoanFreq(String loanFreq) {
        this.loanFreq = loanFreq;
    }

    public String getDueDayOpt() {
        return dueDayOpt;
    }

    public void setDueDayOpt(String dueDayOpt) {
        this.dueDayOpt = dueDayOpt;
    }

    public String getDueDay() {
        return dueDay;
    }

    public void setDueDay(String dueDay) {
        this.dueDay = dueDay;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }
}
