package com.jinghang.capital.core.banks.cybk.dto.repay;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRepayQueryRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.REPAY_QUERY;



    /**
     * 还款编号
     */
    private String outRepaymentSeq;

    /**
     * 设备号
     */
    private String applCde;

    /**
     * 终端类型
     */
    private String setlSeq;

    private String loanNo;

    public String getOutRepaymentSeq() {
        return outRepaymentSeq;
    }

    public void setOutRepaymentSeq(String outRepaymentSeq) {
        this.outRepaymentSeq = outRepaymentSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getSetlSeq() {
        return setlSeq;
    }

    public void setSetlSeq(String setlSeq) {
        this.setlSeq = setlSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }
}
