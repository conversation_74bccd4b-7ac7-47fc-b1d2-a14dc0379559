package com.jinghang.capital.core.vo.recc;




import com.jinghang.capital.core.enums.BankChannel;

import java.time.LocalDate;

/**
 * 对账单查询接口
 */
public class ReccDetailQueryVo {
    /**
     * 资方
     */
    private BankChannel channel;
    /**
     * 对账单流水
     */
    private String cnNo;
    /**
     * 借款id
     */
    private String loanId;
    /**
     * 对账文件类型
     */
    private String reccType;

    /**
     * 对账单生成开始时间
     */
    private LocalDate createDateStart;
    /**
     * 对账单生成结束时间
     */
    private LocalDate createDateEnd;
    /**
     * 对账状态枚举
     */
    private String settlementStatus;

    /**
     * 当前页
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getCnNo() {
        return cnNo;
    }

    public void setCnNo(String cnNo) {
        this.cnNo = cnNo;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getReccType() {
        return reccType;
    }

    public void setReccType(String reccType) {
        this.reccType = reccType;
    }

    public LocalDate getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(LocalDate createDateStart) {
        this.createDateStart = createDateStart;
    }

    public LocalDate getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(LocalDate createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public String getSettlementStatus() {
        return settlementStatus;
    }

    public void setSettlementStatus(String settlementStatus) {
        this.settlementStatus = settlementStatus;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
