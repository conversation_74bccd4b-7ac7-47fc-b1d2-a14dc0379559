package com.jinghang.capital.core.banks.cybk.dto.repay;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


import java.math.BigDecimal;
import java.util.List;
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRepayApplyRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.REPAY_APPLY;

    private String outRepaySeq;
    /**
     * 金融渠道
     */
    private String financeChannel;

    /**
     * 是否已到账
     */
    private String paymInd;

    /**
     * 支付通道
     */
    private String payChannel;
    /**
     * 扣款协议号
     */
    private String agreementNo;
    /**
     * 还款日期
     */
    private String repayDate;
    /**
     * 是否需要入账
     */
    private String needAct;
    /**
     * 对客扣款金额
     */
    private BigDecimal custCutAmt;

    /**
     * 长银还款金额
     */
    private BigDecimal amount;
    /**
     * 担保费
     */
    private String guarAmt;
    /**
     * 担保还款金额
     */
    private BigDecimal guaRepayAmt;
    private String terminalType;   //	终端类型

    private String  merchantNo;	//商户码值
    private String storeCode;	//门店码值
    private String outPayTradeNo;
    /**
     * 归还担保费罚息
     */
    private BigDecimal guarRepayOdAmt;
    private List< RepayInfoRequest> repayInfoList;
    private List< RepayBankRequest> repayBankList;

    public String getFinanceChannel() {
        return financeChannel;
    }

    public void setFinanceChannel(String financeChannel) {
        this.financeChannel = financeChannel;
    }

    public String getPaymInd() {
        return paymInd;
    }

    public void setPaymInd(String paymInd) {
        this.paymInd = paymInd;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public String getNeedAct() {
        return needAct;
    }

    public void setNeedAct(String needAct) {
        this.needAct = needAct;
    }

    public BigDecimal getCustCutAmt() {
        return custCutAmt;
    }

    public void setCustCutAmt(BigDecimal custCutAmt) {
        this.custCutAmt = custCutAmt;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getGuarAmt() {
        return guarAmt;
    }

    public void setGuarAmt(String guarAmt) {
        this.guarAmt = guarAmt;
    }

    public BigDecimal getGuaRepayAmt() {
        return guaRepayAmt;
    }

    public void setGuaRepayAmt(BigDecimal guaRepayAmt) {
        this.guaRepayAmt = guaRepayAmt;
    }

    public BigDecimal getGuarRepayOdAmt() {
        return guarRepayOdAmt;
    }

    public void setGuarRepayOdAmt(BigDecimal guarRepayOdAmt) {
        this.guarRepayOdAmt = guarRepayOdAmt;
    }

    public List<RepayInfoRequest> getRepayInfoList() {
        return repayInfoList;
    }

    public void setRepayInfoList(List<RepayInfoRequest> repayInfoList) {
        this.repayInfoList = repayInfoList;
    }

    public List<RepayBankRequest> getRepayBankList() {
        return repayBankList;
    }

    public void setRepayBankList(List<RepayBankRequest> repayBankList) {
        this.repayBankList = repayBankList;
    }

    public String getOutPayTradeNo() {
        return outPayTradeNo;
    }

    public void setOutPayTradeNo(String outPayTradeNo) {
        this.outPayTradeNo = outPayTradeNo;
    }

    public String getOutRepaySeq() {
        return outRepaySeq;
    }

    public void setOutRepaySeq(String outRepaySeq) {
        this.outRepaySeq = outRepaySeq;
    }

    public String getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }

    //
//    /**
//     * 外部还款流水号
//     */
//    private String outRepaymentSeq;
//    /**
//     * 长银授信流水号
//     */
//    private String applCde;
//    /**
//     * 长银客户号
//     */
//    private String custId;
//    /**
//     * 长银借据号
//     */
//    private String loanNo;
//    /**
//     * 回调地址
//     */
//    private String callbackUrl;
//    /**
//     * 商户码值
//     */
//    private String merchantNo;
//    /**
//     * 门店码值
//     */
//    private String storeCode;
//    /**
//     * 终端类型
//     */
//    private String terminalType;
//    /**
//     * 还款模式
//     */
//    private String repaymentMode;
//    /**
//     * 期数
//     */
//    private String period;
//    /**
//     * 还款金额(长银)
//     */
//    private BigDecimal allTotalOdAmt;
//    /**
//     * 对客扣款金额(资产方)
//     */
//    private BigDecimal custCutAmt;
//    /**
//     * 担保还款金额(担保方)
//     */
//    private BigDecimal guarRepayAmt;
//    /**
//     * 主动还款日期
//     */
//    private String setlApplyDt;
//    /**
//     * 对客还款流水号
//     */
//    private String custRepaySeq;
//    /**
//     * 是否代偿
//     */
//    private String isCompensatory;
//    /**
//     * 还款授权码
//     */
//    private String repaymentCode;
//    /**
//     * 是否已到账
//     */
//    private String paymInd;
//    /**
//     * 资金流水号
//     */
//    private String fundSeqNo;
//    /**
//     * 还款渠道
//     */
//    private String payChannel;
//    /**
//     * 资产支付订单号
//     */
//    private String propertyPayOrderNo;
//    /**
//     * 客户实际还款日
//     */
//    private String custSetlDt;
//    /**
//     * 担保机构清分商户号
//     */
//    private String guarDelsorMerno;
//
//    public String getOutRepaymentSeq() {
//        return outRepaymentSeq;
//    }
//
//    public void setOutRepaymentSeq(String outRepaymentSeq) {
//        this.outRepaymentSeq = outRepaymentSeq;
//    }
//
//    public String getApplCde() {
//        return applCde;
//    }
//
//    public void setApplCde(String applCde) {
//        this.applCde = applCde;
//    }
//
//    public String getCustId() {
//        return custId;
//    }
//
//    public void setCustId(String custId) {
//        this.custId = custId;
//    }
//
//    public String getLoanNo() {
//        return loanNo;
//    }
//
//    public void setLoanNo(String loanNo) {
//        this.loanNo = loanNo;
//    }
//
//    public String getCallbackUrl() {
//        return callbackUrl;
//    }
//
//    public void setCallbackUrl(String callbackUrl) {
//        this.callbackUrl = callbackUrl;
//    }
//
//    public String getMerchantNo() {
//        return merchantNo;
//    }
//
//    public void setMerchantNo(String merchantNo) {
//        this.merchantNo = merchantNo;
//    }
//
//    public String getStoreCode() {
//        return storeCode;
//    }
//
//    public void setStoreCode(String storeCode) {
//        this.storeCode = storeCode;
//    }
//
//    public String getTerminalType() {
//        return terminalType;
//    }
//
//    public void setTerminalType(String terminalType) {
//        this.terminalType = terminalType;
//    }
//
//    public String getRepaymentMode() {
//        return repaymentMode;
//    }
//
//    public void setRepaymentMode(String repaymentMode) {
//        this.repaymentMode = repaymentMode;
//    }
//
//    public String getPeriod() {
//        return period;
//    }
//
//    public void setPeriod(String period) {
//        this.period = period;
//    }
//
//    public BigDecimal getAllTotalOdAmt() {
//        return allTotalOdAmt;
//    }
//
//    public void setAllTotalOdAmt(BigDecimal allTotalOdAmt) {
//        this.allTotalOdAmt = allTotalOdAmt;
//    }
//
//    public BigDecimal getCustCutAmt() {
//        return custCutAmt;
//    }
//
//    public void setCustCutAmt(BigDecimal custCutAmt) {
//        this.custCutAmt = custCutAmt;
//    }
//
//    public BigDecimal getGuarRepayAmt() {
//        return guarRepayAmt;
//    }
//
//    public void setGuarRepayAmt(BigDecimal guarRepayAmt) {
//        this.guarRepayAmt = guarRepayAmt;
//    }
//
//    public String getSetlApplyDt() {
//        return setlApplyDt;
//    }
//
//    public void setSetlApplyDt(String setlApplyDt) {
//        this.setlApplyDt = setlApplyDt;
//    }
//
//    public String getCustRepaySeq() {
//        return custRepaySeq;
//    }
//
//    public void setCustRepaySeq(String custRepaySeq) {
//        this.custRepaySeq = custRepaySeq;
//    }
//
//    public String getIsCompensatory() {
//        return isCompensatory;
//    }
//
//    public void setIsCompensatory(String isCompensatory) {
//        this.isCompensatory = isCompensatory;
//    }
//
//    public String getRepaymentCode() {
//        return repaymentCode;
//    }
//
//    public void setRepaymentCode(String repaymentCode) {
//        this.repaymentCode = repaymentCode;
//    }
//
//    public String getPaymInd() {
//        return paymInd;
//    }
//
//    public void setPaymInd(String paymInd) {
//        this.paymInd = paymInd;
//    }
//
//    public String getFundSeqNo() {
//        return fundSeqNo;
//    }
//
//    public void setFundSeqNo(String fundSeqNo) {
//        this.fundSeqNo = fundSeqNo;
//    }
//
//    public String getPayChannel() {
//        return payChannel;
//    }
//
//    public void setPayChannel(String payChannel) {
//        this.payChannel = payChannel;
//    }
//
//    public String getPropertyPayOrderNo() {
//        return propertyPayOrderNo;
//    }
//
//    public void setPropertyPayOrderNo(String propertyPayOrderNo) {
//        this.propertyPayOrderNo = propertyPayOrderNo;
//    }
//
//    public String getCustSetlDt() {
//        return custSetlDt;
//    }
//
//    public void setCustSetlDt(String custSetlDt) {
//        this.custSetlDt = custSetlDt;
//    }
//
//    public String getGuarDelsorMerno() {
//        return guarDelsorMerno;
//    }
//
//    public void setGuarDelsorMerno(String guarDelsorMerno) {
//        this.guarDelsorMerno = guarDelsorMerno;
//    }
//
//    @Override
//    public CYBKTradeCode getTradeCode() {
//        return TRADE_CODE;
//    }

}
