package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.CYBKCreditFlow;
import com.jinghang.capital.core.entity.CustomerRepayRecord;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 21:08
 */

@Component
public class CYBKCustRepayReccHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCustRepayReccHandler.class);

    private static final int TWO = 2;

    @Autowired
    private WarningService warningService;

    @Override
    public void process(LocalDate reccDay) {
        List<CustomerRepayRecord> repayRecordList = findCustReccRepay(reccDay);

        if (CollectionUtils.isEmpty(repayRecordList)) {
            logger.info("日期" + reccDay.toString() + "无还款记录，不推送长银直连对客还款文件");
        }
        String dateStr = reccDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        File sourceFile = null;
        File tempDir = null;
        File okFile = null;
        try {
            Path path = Files.createTempDirectory("CYBK");
            tempDir = path.toFile();

            String filePre = DateFormatUtils.format(new Date(), "yyyyMMdd") + "对客真实还款流水文件";
            sourceFile = File.createTempFile(tempDir.getAbsolutePath() + filePre, ".csv");
            CSVPrinter printer = CSVFormat.DEFAULT.withSkipHeaderRecord()
                .withDelimiter(',').print(sourceFile, StandardCharsets.UTF_8);

            //表头
            List<String> header = List.of(
                "loan_seq", "appl_seq", "out_appl_seq", "tran_seq", "fund_seq_no", "setl_seq", "trans_type",
                "orig_loan_seq", "orig_setl_seq", "repay_type", "repay_date", "repay_time", "curr_od_days", "curr_loan_os_prcp",
                "curr_prcp_bal", "curr_int_bal", "curr_od_int_bal", "repay_amt", "paid_prcp_amt", "paid_int_amt", "paid_od_int_amt",
                "paid_guarantee_fee_amt", "paid_late_fee_amt", "paid_oth_fee_amt", "paid_pre_repay_fee_amt", "reduction_amt", "reduction_prcp_amt",
                "reduction_int_amt", "reduction_od_int_amt", "reduction_guarantee_fee_amt", "reduction_late_fee_amt", "reduction_oth_fee_amt",
                "reduction_pre_repay_fee_amt", "accrued_status", "write_off");
            printer.printRecord(header);

            for (CustomerRepayRecord repayRecord : repayRecordList) {
                Loan loan = getLoanRepository().findById(repayRecord.getLoanId()).orElseThrow();
                CYBKCreditFlow creditFlow = getCybKCreditFlowRepository().findByCreditId(loan.getCreditId()).orElseThrow();
                BankRepayRecord bankRepayRecord = getBankRepayRecordRepository().findByLoanIdAndPeriodAndRepayStatus(
                    repayRecord.getLoanId(), repayRecord.getPeriod(), ProcessStatus.SUCCESS).orElseThrow();
                String repayType = "02"; //02-批量扣款
                if (RepayType.CLAIM == repayRecord.getRepayType()) {
                    repayType = "05"; //代偿
                } else if (RepayMode.OFFLINE == repayRecord.getRepayMode()) {
                    repayType = "04"; //线下还款
                }

                List<String> custRepayRecordDTOList = new ArrayList<>();
                custRepayRecordDTOList.add(repayRecord.getLoanId());
                custRepayRecordDTOList.add(creditFlow.getCreditNo());
                custRepayRecordDTOList.add(creditFlow.getCreditId());
                custRepayRecordDTOList.add(bankRepayRecord.getId());
                custRepayRecordDTOList.add(repayRecord.getPayOrderNo());
                custRepayRecordDTOList.add(bankRepayRecord.getBankSerial());
                custRepayRecordDTOList.add("1");
                custRepayRecordDTOList.add("");
                custRepayRecordDTOList.add("");
                custRepayRecordDTOList.add(repayType);
                custRepayRecordDTOList.add(repayRecord.getRepayTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
                custRepayRecordDTOList.add(repayRecord.getRepayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                custRepayRecordDTOList.add("");
                custRepayRecordDTOList.add("");
                custRepayRecordDTOList.add("");
                custRepayRecordDTOList.add("");
                custRepayRecordDTOList.add("");
                custRepayRecordDTOList.add(repayRecord.getTotalAmt().movePointRight(TWO).toPlainString());
                custRepayRecordDTOList.add(repayRecord.getPrincipalAmt().movePointRight(TWO).toPlainString());
                custRepayRecordDTOList.add(repayRecord.getInterestAmt().movePointRight(TWO).toPlainString());
                custRepayRecordDTOList.add(bankRepayRecord.getPenaltyAmt().movePointRight(TWO).toPlainString()); //对资罚息
                custRepayRecordDTOList.add(repayRecord.getGuaranteeAmt().movePointRight(TWO).toPlainString());
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("0");
                custRepayRecordDTOList.add("N");
                printer.printRecord(custRepayRecordDTOList);
            }

            printer.close();

            String uploadPath = getCustReccFilePath(reccDay);

            // /download/cyxf/{产品编码}/out/files/{YYYYMMDD}/repay_flow_${yyyymmdd}.csv
            String fileName = "repay_flow_" + dateStr + ".csv";
            String okFileName = "repay_flow_" + dateStr + ".csv.ok";
            // 上传oss
            logger.info("开始长银直连上传对客还款记录");
            getCybkSftpService().upload(uploadPath + fileName, sourceFile.getAbsolutePath().toString());

            // 生成 ok 文件
            Path localVerifyFilePath = Files.createTempFile("repay_flow_" + dateStr, ".csv.ok");
            OutputStream verifyOs = Files.newOutputStream(localVerifyFilePath);
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(String.valueOf(repayRecordList.size()).getBytes(StandardCharsets.UTF_8));
            IOUtils.copy(byteArrayInputStream, verifyOs);

            getCybkSftpService().upload(uploadPath + okFileName, localVerifyFilePath.toAbsolutePath().toString());
            okFile = localVerifyFilePath.toFile();
        } catch (Exception e) {
            logger.error("长银直连上传对客还款记录异常", e);
            warningService.warn("长银直连上传对客还款记录异常:" + e.getMessage());
        } finally {
            if (sourceFile != null) {
                sourceFile.delete();
            }
            if (okFile != null) {
                okFile.delete();
            }
            if (tempDir != null) {
                tempDir.delete();
            }
        }
    }

    @Override
    public ReccType getReccType() {
        return ReccType.CUST_REPAY;
    }
}
