package com.jinghang.capital.core.util;



import com.jinghang.capital.core.dto.RepayPlanCustomDTO;
import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 还款计划试算
 */
public class CoreRepayPlanCalculator {
    private static final BigDecimal MONTH_OF_YEAR = new BigDecimal("12");
    public static final int RATE_SCALE = 8;

    /**
     * 还款计划计算器
     *
     * @param loanAmt    借款金额
     * @param periods    总期数
     * @param bankRate   对资费率irr
     * @param customRate 对客费率irr
     * @return 还款计划
     */
    public static List<RepayPlanCustomDTO> calculate(BigDecimal loanAmt, Integer periods, BigDecimal bankRate, BigDecimal customRate) {
        LocalDate repayDate = LocalDate.now();
        List<RepayPlan> bankPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, repayDate,
                loanAmt, bankRate, periods);

        List<RepayPlan> customPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, repayDate,
                loanAmt, customRate, periods);

        Map<Integer, RepayPlan> bankPlanMap = bankPlanList.stream().collect(Collectors.toMap(RepayPlan::getCurrentPeriod, Function.identity()));
        Map<Integer, RepayPlan> customPlanMap = customPlanList.stream().collect(Collectors.toMap(RepayPlan::getCurrentPeriod, Function.identity()));

        List<RepayPlanCustomDTO> result = new ArrayList<>();
        for (int i = 1; i <= periods; i++) {
            result.add(buildPlan(i, bankPlanMap.get(i), customPlanMap.get(i)));
        }

        return result;

    }

    /**
     * 计算融担费的APR费率
     *
     * @param loanAmt    借款金额
     * @param periods    总期数
     * @param bankRate   对资费率irr
     * @param customRate 对客费率irr
     * @return 还款计划
     */
    public static BigDecimal guaranteeRateApr(BigDecimal loanAmt, Integer periods, BigDecimal bankRate, BigDecimal customRate) {
        List<RepayPlanCustomDTO> resultList = calculate(loanAmt, periods, bankRate, customRate);
        return calculateRateApr(resultList, RepayPlanCustomDTO::getPrincipalAmt, RepayPlanCustomDTO::getGuaranteeAmt);
    }

    /**
     * 计算咨询费的APR费率
     *
     * @param loanAmt    借款金额
     * @param periods    总期数
     * @param bankRate   对资费率irr
     * @param customRate 对客费率irr
     * @return 还款计划
     */
    public static BigDecimal consultRateApr(BigDecimal loanAmt, Integer periods, BigDecimal bankRate, BigDecimal customRate) {
        List<RepayPlanCustomDTO> resultList = calculate(loanAmt, periods, bankRate, customRate);
        return calculateRateApr(resultList, RepayPlanCustomDTO::getPrincipalAmt, RepayPlanCustomDTO::getConsultAmt);
    }

    /**
     * 计算融担费的APR费率
     *
     * @param replanList 还款计划
     * @return APR费率
     */
    public static BigDecimal calculateRateApr(List<RepayPlanCustomDTO> replanList,
                                              Function<RepayPlanCustomDTO, BigDecimal> principalFunc,
                                              Function<RepayPlanCustomDTO, BigDecimal> interestFunc) {
        Integer periods = replanList.size();

        BigDecimal loanAmt = AmountUtil.calcSumAmount(replanList, principalFunc);
        BigDecimal interestTotalAmt = AmountUtil.calcSumAmount(replanList, interestFunc);
        // BigDecimal loanAmt = AmountUtil.calcSumAmount(replanList, RepayPlanCustomDTO::getPrincipalAmt);
        // BigDecimal interestTotalAmt = AmountUtil.calcSumAmount(replanList, RepayPlanCustomDTO::getGuaranteeAmt);

        BigDecimal rate = interestTotalAmt.divide(loanAmt, RATE_SCALE, RoundingMode.HALF_UP)
                .divide(BigDecimal.valueOf(periods), RATE_SCALE, RoundingMode.HALF_UP)
                .multiply(MONTH_OF_YEAR);


        return rate;

    }


    private static RepayPlanCustomDTO buildPlan(Integer period, RepayPlan bankPlan, RepayPlan customPlan) {
        RepayPlanCustomDTO dto = new RepayPlanCustomDTO();
        dto.setPeriod(period);
        dto.setRepayDate(bankPlan.getRepayDate());
        dto.setPrincipalAmt(bankPlan.getPrincipal());
        dto.setInterestAmt(bankPlan.getInterest());

        BigDecimal guaranteeAmt = customPlan.getPrincipal().add(customPlan.getInterest())
                .subtract(bankPlan.getPrincipal()).subtract(bankPlan.getInterest());
        dto.setGuaranteeAmt(guaranteeAmt);
        dto.setConsultAmt(BigDecimal.ZERO);

        return dto;
    }


}
