package com.jinghang.capital.core.banks.cybk.enums;

public enum CYBKRepayType {
    REPAY_CURRENT("0", "正常还当期"),
    REPAY_OVERDUE("1", "逾期还当期"),
    REPAY_CLEAR("2", "全部结清"),
    REPAY_AMOUNT("3", "按金额还款"),
    REPAY_ADV_CURRENT("4", "提前还当期"),
    CLAIM_CURRENT("5", "代偿当期"),
    CLAIM_CLEAR("6", "代偿整笔借据（回购）");
    private final String code;

    private final String desc;

    CYBKRepayType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    CYBKRepayType findByCode(String code) {
        CYBKRepayType[] values = CYBKRepayType.values();
        for (int i = 0; i < values.length; i++) {
            CYBKRepayType status = values[i];
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new RuntimeException("长银直连还款类型未找到");
    }
}
