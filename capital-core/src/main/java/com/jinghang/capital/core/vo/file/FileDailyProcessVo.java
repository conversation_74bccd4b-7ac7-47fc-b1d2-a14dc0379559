package com.jinghang.capital.core.vo.file;



import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;

import java.time.LocalDate;

/**
 * 每日文件处理
 */
public class FileDailyProcessVo {

    /**
     * 需要处理日期
     */
    private LocalDate processDate;


    private FileType type;

    private BankChannel bankChannel;

    private String loanId;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public LocalDate getProcessDate() {
        return processDate;
    }

    public void setProcessDate(LocalDate processDate) {
        this.processDate = processDate;
    }

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }
}
