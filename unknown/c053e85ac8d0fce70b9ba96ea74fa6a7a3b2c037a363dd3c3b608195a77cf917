package com.jinghang.capital.core.util;

import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;

public class RateLimiterUtil {


    /**
     * 获取RateLimiter
     * @param name RateLimiter的名称
     * @param rate 速率（每rateInterval单位时间允许的操作数）
     * @param rateInterval 时间间隔
     * @param rateIntervalUnit 时间间隔单位
     */
    public static RRateLimiter getRateLimiter(String name, long rate, long rateInterval, RateIntervalUnit rateIntervalUnit, RedissonClient client) {
        RRateLimiter rateLimiter = client.getRateLimiter(name);
        rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        return rateLimiter;
    }

    /**
     * 尝试获取权限
     * @param rateLimiter RateLimiter对象
     * @return 是否获取成功
     */
    public static boolean tryAcquire(RRateLimiter rateLimiter) {
        return rateLimiter.tryAcquire();
    }

}
