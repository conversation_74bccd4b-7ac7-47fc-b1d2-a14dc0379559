package com.jinghang.capital.core.vo.recc;

import java.util.List;

/**
 * 对账单详情查询结果
 */
public class ReccDetailQueryResultVo {
    /**
     * 对账单详情查询结果
     */
    private List<ReccDetail> reccDetails;

    /**
     * 当前页
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;
    /**
     * 总条数
     */
    private long total;

    public List<ReccDetail> getReccDetails() {
        return reccDetails;
    }

    public void setReccDetails(List<ReccDetail> reccDetails) {
        this.reccDetails = reccDetails;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
