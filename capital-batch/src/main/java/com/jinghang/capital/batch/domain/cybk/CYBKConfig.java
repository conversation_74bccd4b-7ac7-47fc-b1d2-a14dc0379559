package com.jinghang.capital.batch.domain.cybk;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CYBKConfig {

    @Value("${cybk.sftp.username}")
    private String sftpUsername;
    @Value("${cybk.sftp.password}")
    private String sftpPassword;
    @Value("${cybk.sftp.host}")
    private String sftpHost;
    @Value("${cybk.sftp.port}")
    private Integer sftpPort;
    @Value("${cybk.sftp.upload.dir}")
    private String uploadDir;

    @Value("${cybk.sftp.download.dir}")
    private String downloadDir;

    @Value("${cybk.loanType}")
    private String loanType;

    public String getLoanType() {
        return loanType;
    }

    public String getSftpUsername() {
        return sftpUsername;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public String getDownloadDir() {
        return downloadDir;
    }

    public String getUploadDir() {
        return uploadDir;
    }
}
