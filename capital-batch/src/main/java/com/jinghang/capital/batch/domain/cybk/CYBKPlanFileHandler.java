package com.jinghang.capital.batch.domain.cybk;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.batch.entity.*;
import com.jinghang.capital.batch.service.FileService;
import com.jinghang.capital.batch.service.cybk.CYBKReccClaimService;
import com.jinghang.capital.batch.service.cybk.CYBKReccPlanService;
import com.jinghang.capital.batch.service.cybk.CYBKReccRepayDetailService;
import com.jinghang.capital.batch.service.tc.impl.CreditServiceImpl;
import com.jinghang.common.exception.BizException;
import com.jinghang.common.exception.code.SysErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 长银消金直连 还款计划文件 新增加
 */
@Component
public class CYBKPlanFileHandler extends AbstractCYBKReconFileHandler<CYBKReccPlan> {

    private static final Logger logger = LoggerFactory.getLogger(CYBKRepayDetailFileHandler.class);


    private static final int ID_0 = 0;
    private static final int ID_1 = 1;
    private static final int ID_2 = 2;
    private static final int ID_3 = 3;
    private static final int ID_4 = 4;
    private static final int ID_5 = 5;
    private static final int ID_6 = 6;
    private static final int ID_7 = 7;
    private static final int ID_8 = 8;
    private static final int ID_9 = 9;
    private static final int ID_10 = 10;



    //   /upload/cyxf/{产品编码}/in/files/{YYYYMMDD}/repayment_plan_${yyyyMMdd}.csv
    // 长银还款后还款计划
    private static final String RECC_FILE_NAME = "%s/plan_%s.csv";


    private static final Integer LINE_LENGTH = 11;

    @Autowired
    private FileService fileService;

    @Autowired
    private CYBKReccPlanService cybkReccPlanService;

    @Autowired
    private CreditServiceImpl creditService;


    @Override
    protected String ossFilePath(LocalDate data) {
        return "cybk/recc/plan/" + getFileName(data);
    }

    @Override
    protected String sftpFilePath(LocalDate data) {
        return getSftpPathPrefix() + "/" + getFileName(data);
    }


    private String getFileName(LocalDate data) {
        String dateStr1 = data.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return RECC_FILE_NAME.formatted(dateStr1, dateStr1);
    }

    @Override
    protected List<CYBKReccPlan> getReccFileDetails(InputStream inputStream, StringBuffer sftpFileStr) {
        List<CYBKReccPlan> reccRepayList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            int lineNum = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (line.isEmpty()) {
                    continue;
                }
                lineNum++;
                if (lineNum == 1) {
                    continue;
                }
                appendStr(sftpFileStr, line);
                String[] lineArr = line.split(SEPARATOR, LINE_LENGTH);
                if (lineArr.length < LINE_LENGTH) {
                    throw new RuntimeException("长银消金直连, 解析还款计划数据异常,line:" + (reccRepayList.size() + 1) + "__" + line);
                }

                // 长银文档中的对方业务号
                String loanId = lineArr[ID_0];
                if (!loanId.startsWith("LO")) {
                    logger.info("reccPlan loanId: [{}] 非本平台固有借据号格式 忽略", loanId);
                    continue;
                }
                Loan loan = getLoanService().findByIdAndChannel(loanId, BankChannel.CYBK.name());
                if (Objects.isNull(loan)) {
                    logger.info("reccPlan loanId: [{}] 未查到数据库匹配信息 忽略", loanId);
                    continue;
                }

                CYBKReccPlan entity = new CYBKReccPlan();
                entity.setReccRepayId("");
                entity.setCreditId(loan.getCreditId());
                // 长银文档中的长银借据号
                entity.setCybkLoanNo(lineArr[ID_1]);
                entity.setPeriod(Integer.parseInt(lineArr[ID_2]));
                entity.setDueDate(LocalDate.parse(lineArr[ID_3], DateTimeFormatter.ISO_LOCAL_DATE));
                entity.setRepayAmount(new BigDecimal(lineArr[ID_4]));
                // 本金金额
                entity.setRepayPrincipalAmt(new BigDecimal(lineArr[ID_5]));
                // 利息
                entity.setRepayInterestAmt(new BigDecimal(lineArr[ID_6]));
                // 罚息
                entity.setRepayPenaltyAmt(new BigDecimal(lineArr[ID_7]));
                // 复利金额
                entity.setRepayReinterestAmt(new BigDecimal(lineArr[ID_8]));
                // 担保费金额
                entity.setRepayGuaranteeAmt(new BigDecimal(lineArr[ID_9]));
                // 担保费逾期费用
                entity.setRepayGuaranteeOverdueAmt(new BigDecimal(lineArr[ID_10]));

                if (loan != null) {
                    entity.setCreditId(loan.getCreditId());
                    entity.setSysId(loan.getId());
                    entity.setChannel(loan.getChannel());
                }

                reccRepayList.add(entity);
            }
        } catch (Exception e) {
            logger.error("CYBK 还款计划文件解析异常: ", e);
            throw new RuntimeException("解析还款计划文件失败");
        }
        return reccRepayList;
    }

    @Override
    protected void saveEntity(List<CYBKReccPlan> entityList) {
        cybkReccPlanService.saveBatch(entityList, DEFAULT_BATCH_SIZE);
    }

    @Override
    protected void fillEntityData(List<CYBKReccPlan> entityList, CYBKReconcileFile reconcileFile) {
        entityList.forEach(e -> {
            e.setReccStatus(null);
            e.setReccId(reconcileFile.getId());
            e.setCreatedTime(LocalDateTime.now());
            e.setUpdatedTime(LocalDateTime.now());
        });
    }


    @Override
    public CYBKReccFileTypeEnum getReccType() {
        return CYBKReccFileTypeEnum.PLAN_FILE;
    }
}
